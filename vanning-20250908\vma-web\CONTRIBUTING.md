# Contributing

## Branching

- `main`: protected, release-ready
- feature branches: `feat/<scope>-<short-desc>`
- fix branches: `fix/<scope>-<short-desc>`
- docs branches: `docs/<short-desc>`

## Commits

- Use clear, imperative messages (e.g., "add settings import button")
- Group related changes; keep commits small and focused
- Follow conventional commit format when possible

## Pull Requests

- One feature/fix per PR
- Include description, screenshots (UI), and testing notes
- Ensure CI/build passes, no lint/type errors
- Update documentation if needed

## Code Style

- TypeScript strictness; avoid `any`
- Use meaningful names, early returns, shallow nesting
- Prettier + EditorConfig govern formatting
- ESLint for lint rules
- Follow React best practices and hooks rules

## Testing

- Add unit/integration tests where applicable
- Ensure `npm run typecheck`, `npm run lint`, and `npm run test` pass
- Maintain good test coverage for new features
- Use React Testing Library for component testing
- Test both success and error scenarios

## Code Quality

- Run `npm run lint` before committing to catch issues
- Use `npm run format` to ensure consistent code formatting
- All tests must pass before submitting PRs
- Follow the established project structure and patterns

## Versioning & Releases

- Use changesets for versioning: `npm run changeset`
- Document breaking changes clearly
- Follow semantic versioning principles
- Coordinate releases with the team

## Documentation

- Update README/SETUP if workflows change
- Document new env vars in `src/core/config/env.ts` and README
- Add JSDoc comments for complex functions and components
- Keep API documentation up-to-date

## Review Process

- All PRs require at least one review
- Address review comments promptly
- Ensure CI checks pass before merging
- Squash commits when appropriate
