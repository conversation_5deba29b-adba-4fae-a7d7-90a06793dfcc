import ThemeProvider from './core/providers/ThemeProvider'
import AuthProvider from './core/providers/AuthProvider'
import { RouterProvider, createBrowserRouter } from 'react-router-dom'
import { routes } from './app/routes'
import { Suspense, useEffect, useState } from 'react'
import Loader from './common/components/Loader'
import ErrorBoundary from './core/error/ErrorBoundary'
import { useAppStore } from './core/store'
import { initI18n } from './core/i18n'

const router = createBrowserRouter(routes)

export default function App() {
	const locale = useAppStore((s) => s.locale)
	const [i18nInitialized, setI18nInitialized] = useState(false)
	const [storeHydrated, setStoreHydrated] = useState(false)

	// Wait for store hydration from localStorage
	useEffect(() => {
		const unsubscribe = useAppStore.persist.onFinishHydration(() => {
			setStoreHydrated(true)
		})

		// Check if already hydrated
		if (useAppStore.persist.hasHydrated()) {
			setStoreHydrated(true)
		}

		return unsubscribe
	}, [])

	// Initialize i18n after store is hydrated to use the correct locale
	useEffect(() => {
		if (!storeHydrated) return

		initI18n(locale).then(() => {
			setI18nInitialized(true)
		}).catch((error) => {
			console.error('Failed to initialize i18n:', error)
			setI18nInitialized(true) // Still render the app even if i18n fails
		})
	}, [locale, storeHydrated])

	if (!storeHydrated || !i18nInitialized) {
		return <Loader />
	}

	return (
		<ThemeProvider>
			<AuthProvider>
				<ErrorBoundary>
					<Suspense fallback={<Loader />}>
						<RouterProvider router={router} />
					</Suspense>
				</ErrorBoundary>
			</AuthProvider>
		</ThemeProvider>
	)
}
