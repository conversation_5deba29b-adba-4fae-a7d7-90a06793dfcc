package com.inspeedia.vanning.validation;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for validation annotations on domain entities
 */
class ValidationTest {

    private Validator validator;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testValidActualWork() {
        ActualWork validWork = new ActualWork();
        validWork.setOperatorName("田中太郎");
        validWork.setWorkDate(LocalDate.of(2024, 1, 15));
        validWork.setStartTime(LocalTime.of(8, 0));
        validWork.setEndTime(LocalTime.of(17, 0));
        validWork.setDuration(LocalTime.of(9, 0));
        validWork.setProgress(8);
        validWork.setProgressRate(80.0f);
        validWork.setVanGp("AB");
        validWork.setCompleted(false);

        Set<ConstraintViolation<ActualWork>> violations = validator.validate(validWork);
        assertTrue(violations.isEmpty(), "Valid ActualWork should have no validation errors");
    }

    @Test
    void testInvalidActualWorkBlankOperatorName() {
        ActualWork invalidWork = new ActualWork();
        invalidWork.setOperatorName(""); // Invalid - blank
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));
        invalidWork.setProgress(8);
        invalidWork.setProgressRate(80.0f);
        invalidWork.setVanGp("AB");
        invalidWork.setCompleted(false);

        Set<ConstraintViolation<ActualWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("operatorName")));
    }

    @Test
    void testInvalidActualWorkProgressOutOfRange() {
        ActualWork invalidWork = new ActualWork();
        invalidWork.setOperatorName("田中太郎");
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));
        invalidWork.setProgress(15); // Invalid - out of range
        invalidWork.setProgressRate(80.0f);
        invalidWork.setVanGp("AB");
        invalidWork.setCompleted(false);

        Set<ConstraintViolation<ActualWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("progress")));
    }

    @Test
    void testInvalidActualWorkProgressRateOutOfRange() {
        ActualWork invalidWork = new ActualWork();
        invalidWork.setOperatorName("田中太郎");
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));
        invalidWork.setProgress(8);
        invalidWork.setProgressRate(150.0f); // Invalid - out of range
        invalidWork.setVanGp("AB");
        invalidWork.setCompleted(false);

        Set<ConstraintViolation<ActualWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("progressRate")));
    }

    @Test
    void testValidPlannedWork() {
        PlannedWork validWork = new PlannedWork();
        validWork.setWorkDate(LocalDate.of(2024, 1, 15));
        validWork.setOperatorName("田中太郎");
        validWork.setVanGp("AB");
        validWork.setDeliveryPlatform("A");
        validWork.setCollectionPlatform("B");
        validWork.setLoadTime(LocalTime.of(7, 30));
        validWork.setSize("L1");
        validWork.setStartTime(LocalTime.of(8, 0));
        validWork.setEndTime(LocalTime.of(17, 0));
        validWork.setDuration(LocalTime.of(9, 0));

        Set<ConstraintViolation<PlannedWork>> violations = validator.validate(validWork);
        assertTrue(violations.isEmpty(), "Valid PlannedWork should have no validation errors");
    }

    @Test
    void testInvalidPlannedWorkVanGpWrongLength() {
        PlannedWork invalidWork = new PlannedWork();
        invalidWork.setWorkDate(LocalDate.of(2024, 1, 15));
        invalidWork.setOperatorName("田中太郎");
        invalidWork.setVanGp("ABCD"); // Invalid - should be 1-3 characters
        invalidWork.setDeliveryPlatform("A");
        invalidWork.setCollectionPlatform("B");
        invalidWork.setLoadTime(LocalTime.of(7, 30));
        invalidWork.setSize("L1");
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));

        Set<ConstraintViolation<PlannedWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("vanGp")));
    }

    @Test
    void testInvalidPlannedWorkVanGpWrongPattern() {
        PlannedWork invalidWork = new PlannedWork();
        invalidWork.setWorkDate(LocalDate.of(2024, 1, 15));
        invalidWork.setOperatorName("田中太郎");
        invalidWork.setVanGp("ab"); // Invalid - should be uppercase
        invalidWork.setDeliveryPlatform("A");
        invalidWork.setCollectionPlatform("B");
        invalidWork.setLoadTime(LocalTime.of(7, 30));
        invalidWork.setSize("L1");
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));

        Set<ConstraintViolation<PlannedWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("vanGp")));
    }

    @Test
    void testInvalidPlannedWorkPlatformWrongLength() {
        PlannedWork invalidWork = new PlannedWork();
        invalidWork.setWorkDate(LocalDate.of(2024, 1, 15));
        invalidWork.setOperatorName("田中太郎");
        invalidWork.setVanGp("AB");
        invalidWork.setDeliveryPlatform("AB"); // Invalid - should be 1 character
        invalidWork.setCollectionPlatform("B");
        invalidWork.setLoadTime(LocalTime.of(7, 30));
        invalidWork.setSize("L1");
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));

        Set<ConstraintViolation<PlannedWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("deliveryPlatform")));
    }

    @Test
    void testInvalidPlannedWorkSizeWrongLength() {
        PlannedWork invalidWork = new PlannedWork();
        invalidWork.setWorkDate(LocalDate.of(2024, 1, 15));
        invalidWork.setOperatorName("田中太郎");
        invalidWork.setVanGp("AB");
        invalidWork.setDeliveryPlatform("A");
        invalidWork.setCollectionPlatform("B");
        invalidWork.setLoadTime(LocalTime.of(7, 30));
        invalidWork.setSize("L"); // Invalid - should be 2 characters
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));

        Set<ConstraintViolation<PlannedWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("size")));
    }

    @Test
    void testActualWorkOperatorNameTooLong() {
        ActualWork invalidWork = new ActualWork();
        invalidWork.setOperatorName("A".repeat(101)); // Invalid - too long
        invalidWork.setStartTime(LocalTime.of(8, 0));
        invalidWork.setEndTime(LocalTime.of(17, 0));
        invalidWork.setDuration(LocalTime.of(9, 0));
        invalidWork.setProgress(8);
        invalidWork.setProgressRate(80.0f);
        invalidWork.setVanGp("AB");
        invalidWork.setCompleted(false);

        Set<ConstraintViolation<ActualWork>> violations = validator.validate(invalidWork);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("operatorName")));
    }

    @Test
    void testMultipleValidationErrors() {
        ActualWork invalidWork = new ActualWork();
        invalidWork.setOperatorName(""); // Invalid - blank
        invalidWork.setProgress(15); // Invalid - out of range
        invalidWork.setProgressRate(-10.0f); // Invalid - negative

        Set<ConstraintViolation<ActualWork>> violations = validator.validate(invalidWork);
        assertTrue(violations.size() >= 3, "Should have multiple validation errors");

        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("operatorName")));
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("progress")));
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("progressRate")));
    }
}
