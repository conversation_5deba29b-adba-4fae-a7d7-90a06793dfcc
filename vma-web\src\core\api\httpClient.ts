import { ENV } from '../config/env'

function buildUrl(url: string): string {
	// If URL is already absolute, return as-is
	if (url.startsWith('http://') || url.startsWith('https://')) {
		return url
	}

	// If we have an API base URL configured, use it
	if (ENV.API_BASE_URL) {
		const fullUrl = `${ENV.API_BASE_URL}${url}`
		if (ENV.DEBUG_HTTP) {
			console.debug(`[HTTP Client] Using configured API base URL: ${fullUrl}`)
		}
		return fullUrl
	}

	// Otherwise, use relative URL (for development with proxy)
	if (ENV.DEBUG_HTTP) {
		console.debug(`[HTTP Client] Using relative URL for proxy: ${url}`)
	}
	return url
}

export async function httpGet<T>(
	url: string,
	init?: RequestInit & { signal?: AbortSignal },
): Promise<T> {
	const fullUrl = buildUrl(url)
	const res = await fetch(fullUrl, { ...init })
	if (!res.ok) throw new Error(`HTTP ${res.status}`)
	return (await res.json()) as T
}

export async function httpPost<T>(
	url: string,
	body?: FormData | string | object,
	init?: RequestInit & { signal?: AbortSignal },
): Promise<T> {
	const fullUrl = buildUrl(url)

	let requestBody: FormData | string | undefined
	const headers: Record<string, string> = { ...init?.headers } as Record<string, string>

	if (body instanceof FormData) {
		// Let the browser set Content-Type for FormData (includes boundary)
		requestBody = body
		if (ENV.DEBUG_HTTP) {
			console.debug(
				`[HTTP Client] POST ${fullUrl} with FormData (${body.get('file') ? 'file upload' : 'form data'})`,
			)
		}
	} else if (typeof body === 'string') {
		requestBody = body
		headers['Content-Type'] = 'text/plain'
		if (ENV.DEBUG_HTTP) {
			console.debug(`[HTTP Client] POST ${fullUrl} with text body`)
		}
	} else if (body && typeof body === 'object') {
		requestBody = JSON.stringify(body)
		headers['Content-Type'] = 'application/json'
		if (ENV.DEBUG_HTTP) {
			console.debug(`[HTTP Client] POST ${fullUrl} with JSON body`)
		}
	} else {
		if (ENV.DEBUG_HTTP) {
			console.debug(`[HTTP Client] POST ${fullUrl} with no body`)
		}
	}

	try {
		const res = await fetch(fullUrl, {
			...init,
			method: 'POST',
			headers,
			body: requestBody,
		})

		if (ENV.DEBUG_HTTP) {
			console.debug(`[HTTP Client] Response: ${res.status} ${res.statusText}`)
		}

		if (!res.ok) {
			const errorText = await res.text().catch(() => 'Unknown error')
			console.error(`[HTTP Client] Error ${res.status}: ${errorText}`)
			throw new Error(`HTTP ${res.status}: ${res.statusText}`)
		}

		return (await res.json()) as T
	} catch (error) {
		console.error(`[HTTP Client] Request failed:`, error)
		throw error
	}
}
