# Planned Work Import Debugging Guide

## Issues Identified and Fixed

### 1. Critical Data Structure Mismatch ✅ FIXED

**Problem**: The frontend TypeScript interface didn't match the backend API response structure.

**Backend Response (`ImportResultDto`)**:
```java
{
    "totalRecords": 10,
    "successfulInserts": 7,
    "successfulUpdates": 2,
    "failedRecords": 1,
    "errors": ["Row 5: Invalid date format"],
    "warnings": [],
    "message": "Import completed: 7 inserted, 2 updated, 1 failed"
}
```

**Frontend Interface (OLD - BROKEN)**:
```typescript
interface PlannedWorkImportResponse {
    success: boolean        // ❌ NOT in backend response
    message: string        // ✅ Matches
    importedCount: number  // ❌ Should be successfulInserts + successfulUpdates
    skippedCount: number   // ❌ Should be failedRecords
    errors?: string[]      // ✅ Matches
}
```

**Frontend Interface (NEW - FIXED)**:
```typescript
interface PlannedWorkImportResponse {
    totalRecords: number
    successfulInserts: number
    successfulUpdates: number
    failedRecords: number
    errors: string[]
    warnings: string[]
    message: string
}
```

### 2. Frontend Logic Issues ✅ FIXED

**Problem**: Frontend code expected a `success` boolean field that didn't exist.

**OLD Code (BROKEN)**:
```typescript
if (response.success) {  // ❌ 'success' field doesn't exist
    showNotification(`Successfully imported ${response.importedCount} tasks`, 'success')
}
```

**NEW Code (FIXED)**:
```typescript
const totalSuccessful = response.successfulInserts + response.successfulUpdates
const isSuccess = totalSuccessful > 0

if (isSuccess) {
    showNotification(
        `Successfully imported ${totalSuccessful} tasks (${response.successfulInserts} new, ${response.successfulUpdates} updated)`,
        'success'
    )
}
```

### 3. Environment Configuration ✅ FIXED

**Problem**: Hardcoded API base URL and `import.meta` compatibility issues with Jest.

**OLD Code (BROKEN)**:
```typescript
export const ENV = {
    API_BASE_URL: 'http://localhost:8080',  // ❌ Hardcoded
}
```

**NEW Code (FIXED)**:
```typescript
export const ENV = {
    // In development, Vite proxy will handle /api/v1 requests
    // In production, set VITE_API_BASE_URL environment variable
    API_BASE_URL: '',
}
```

## How to Debug Import Issues

### 1. Check Network Requests

Open browser DevTools → Network tab and look for:
- **Request URL**: Should be `/api/v1/planned-work/import`
- **Request Method**: Should be `POST`
- **Content-Type**: Should be `multipart/form-data`
- **Request Body**: Should contain the Excel file

### 2. Check Console Logs

Look for these log messages:
- **Success**: "Import warnings:" or "Import errors:" (if any)
- **Errors**: Any JavaScript errors or network failures

### 3. Check Backend Logs

Look for these log messages in the Spring Boot application:
- `Starting Excel import for file: {filename}`
- `Excel import failed: {error message}`
- Any validation errors from the ExcelImportService

### 4. Verify File Format

Ensure the Excel file matches the expected format:
- **Columns**: Work Date, Operator Name, Van GP, Delivery Platform, Collection Platform, Load Time, Size, Start Time, End Time, Duration
- **File Types**: `.xlsx` or `.xls`
- **File Size**: Maximum 10MB
- **Data Validation**: Van GP should be exactly 2 characters (A-Z, 0-9)

### 5. Test with Sample Data

Use the template download endpoint to get a properly formatted Excel file:
```
GET /api/v1/planned-work/import/template
```

## Common Error Scenarios

### Scenario 1: "Invalid file type" Error
- **Cause**: File is not .xlsx or .xls
- **Solution**: Use proper Excel format

### Scenario 2: "File size too large" Error
- **Cause**: File exceeds 10MB limit
- **Solution**: Reduce file size or split into smaller files

### Scenario 3: Network Error (HTTP 500)
- **Cause**: Backend processing error
- **Solution**: Check backend logs for specific error details

### Scenario 4: Validation Errors
- **Cause**: Data doesn't match validation rules
- **Solution**: Check error messages for specific row/column issues

### Scenario 5: No Response/Silent Failure
- **Cause**: Network connectivity or CORS issues
- **Solution**: Check network tab and ensure backend is running

## Testing the Fix

Run the test suite to verify everything works:
```bash
npm test -- --testPathPattern="plannedWorkImport.test.ts"
```

All tests should pass, confirming:
- ✅ File validation works correctly
- ✅ API calls are made with proper parameters
- ✅ Response handling matches backend structure
- ✅ Error scenarios are handled properly

## Production Deployment Notes

1. **Environment Variables**: Set `VITE_API_BASE_URL` if needed for production
2. **CORS Configuration**: Ensure backend allows file uploads from frontend domain
3. **File Upload Limits**: Verify server-side file size limits match frontend validation
4. **Error Monitoring**: Monitor backend logs for import failures
