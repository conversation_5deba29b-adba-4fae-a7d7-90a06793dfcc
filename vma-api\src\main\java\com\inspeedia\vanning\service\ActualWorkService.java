package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.repository.ActualWorkRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Service class for ActualWork business logic. This service provides business
 * operations for ActualWork entities with reactive programming patterns and
 * proper error handling.
 */
@Service
@Transactional
public class ActualWorkService {

    private final ActualWorkRepository actualWorkRepository;
    private static final Logger log = LoggerFactory.getLogger(ActualWorkService.class);

    public ActualWorkService(ActualWorkRepository actualWorkRepository) {
        this.actualWorkRepository = actualWorkRepository;
    }

    /**
     * Create a new actual work record
     */
    public Mono<ActualWork> createActualWork(ActualWork actualWork) {
        log.debug("Creating new actual work for operator: {}", actualWork.getOperatorName());

        actualWork.setCreatedAt(LocalDateTime.now());
        actualWork.setUpdatedAt(LocalDateTime.now());

        return actualWorkRepository.save(actualWork)
                .doOnSuccess(savedWork -> log.info("Actual work created successfully with ID: {}", savedWork.getId()))
                .doOnError(error -> log.error("Error creating actual work: {}", error.getMessage()));
    }

    /**
     * Update an existing actual work record
     */
    public Mono<ActualWork> updateActualWork(Long id, ActualWork actualWork) {
        log.debug("Updating actual work with ID: {}", id);

        return actualWorkRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Actual work not found with ID: " + id)))
                .flatMap(existingWork -> {
                    // Update fields
                    existingWork.setOperatorName(actualWork.getOperatorName());
                    existingWork.setWorkDate(actualWork.getWorkDate());
                    existingWork.setStartTime(actualWork.getStartTime());
                    existingWork.setEndTime(actualWork.getEndTime());
                    existingWork.setDuration(actualWork.getDuration());
                    existingWork.setProgress(actualWork.getProgress());
                    existingWork.setProgressRate(actualWork.getProgressRate());
                    existingWork.setUpdatedAt(LocalDateTime.now());

                    return actualWorkRepository.save(existingWork);
                })
                .doOnSuccess(updatedWork -> log.info("Actual work updated successfully with ID: {}", updatedWork.getId()))
                .doOnError(error -> log.error("Error updating actual work: {}", error.getMessage()));
    }

    /**
     * Get actual work by ID
     */
    public Mono<ActualWork> getActualWorkById(Long id) {
        log.debug("Fetching actual work with ID: {}", id);

        return actualWorkRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Actual work not found with ID: " + id)))
                .doOnSuccess(work -> log.debug("Actual work found for operator: {}", work.getOperatorName()));
    }

    /**
     * Get all active actual work with pagination
     */
    public Flux<ActualWork> getAllActiveActualWork(int page, int size) {
        log.debug("Fetching active actual work - page: {}, size: {}", page, size);

        long offset = (long) page * size;
        return actualWorkRepository.findAllActive(offset, size)
                .doOnComplete(() -> log.debug("Completed fetching active actual work"));
    }

    /**
     * Get actual work by user name
     */
    public Flux<ActualWork> getActualWorkByUserName(String userName) {
        log.debug("Fetching actual work for user: {}", userName);

        return actualWorkRepository.findByOperatorName(userName);
    }

    /**
     * Search actual work by user name
     */
    public Flux<ActualWork> searchActualWorkByUserName(String searchTerm) {
        log.debug("Searching actual work with term: {}", searchTerm);

        return actualWorkRepository.searchByUserName(searchTerm)
                .doOnComplete(() -> log.debug("Completed actual work search"));
    }

    /**
     * Get actual work by progress
     */
    public Flux<ActualWork> getActualWorkByProgress(Integer progress) {
        log.debug("Fetching actual work with progress: {}", progress);

        return actualWorkRepository.findByProgress(progress);
    }

    /**
     * Get actual work by progress range
     */
    public Flux<ActualWork> getActualWorkByProgressRange(Integer minProgress, Integer maxProgress) {
        log.debug("Fetching actual work with progress between {} and {}", minProgress, maxProgress);

        return actualWorkRepository.findByProgressBetween(minProgress, maxProgress);
    }

    /**
     * Get actual work by progress rate range
     */
    public Flux<ActualWork> getActualWorkByProgressRateRange(Float minRate, Float maxRate) {
        log.debug("Fetching actual work with progress rate between {} and {}", minRate, maxRate);

        return actualWorkRepository.findByProgressRateBetween(minRate, maxRate);
    }

    /**
     * Get high progress work
     */
    public Flux<ActualWork> getHighProgressWork() {
        log.debug("Fetching high progress work");

        return actualWorkRepository.findHighProgressWork();
    }

    /**
     * Get low progress work
     */
    public Flux<ActualWork> getLowProgressWork() {
        log.debug("Fetching low progress work");

        return actualWorkRepository.findLowProgressWork();
    }

    /**
     * Get actual work by start time range
     */
    public Flux<ActualWork> getActualWorkByStartTimeRange(LocalTime startTime, LocalTime endTime) {
        log.debug("Fetching actual work with start time between {} and {}", startTime, endTime);

        return actualWorkRepository.findByStartTimeBetween(startTime, endTime);
    }

    /**
     * Soft delete actual work
     */
    public Mono<Void> deleteActualWork(Long id) {
        log.debug("Soft deleting actual work with ID: {}", id);

        return actualWorkRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Actual work not found with ID: " + id)))
                .flatMap(work -> actualWorkRepository.softDeleteById(id))
                .then()
                .doOnSuccess(unused -> log.info("Actual work soft deleted successfully with ID: {}", id))
                .doOnError(error -> log.error("Error deleting actual work: {}", error.getMessage()));
    }

    /**
     * Count actual work by user name
     */
    public Mono<Long> countActualWorkByUserName(String userName) {
        log.debug("Counting actual work for user: {}", userName);

        return actualWorkRepository.countByUserName(userName);
    }

    /**
     * Get actual work by work date
     */
    public Flux<ActualWork> getActualWorkByWorkDate(LocalDate workDate) {
        log.debug("Fetching actual work for date: {}", workDate);

        return actualWorkRepository.findByWorkDate(workDate);
    }

    /*
    * Get actual work for current date
    * */
    public Flux<ActualWork> getActualWorkForCurrentDate() {
        log.debug("Fetching actual work for current date: {}", LocalDate.now());
        return actualWorkRepository.findWorkByTodayDate();
    }

    /**
     * Get actual work by work date range
     */
    public Flux<ActualWork> getActualWorkByWorkDateBetween(LocalDate startDate, LocalDate endDate) {
        log.debug("Fetching actual work between dates: {} and {}", startDate, endDate);

        return actualWorkRepository.findByWorkDateBetween(startDate, endDate);
    }

    /**
     * Get actual work by user name and work date
     */
    public Flux<ActualWork> getActualWorkByUserNameAndWorkDate(String userName, LocalDate workDate) {
        log.debug("Fetching actual work for user: {} on date: {}", userName, workDate);

        return actualWorkRepository.findByOperatorNameAndWorkDate(userName, workDate);
    }
}
