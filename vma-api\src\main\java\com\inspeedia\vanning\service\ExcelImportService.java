package com.inspeedia.vanning.service;

import com.inspeedia.vanning.config.ExcelColumnMapping;
import com.inspeedia.vanning.config.ExcelImportConfig;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.ImportResultDto;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.inspeedia.vanning.service.excel.ExcelStreamingReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.inspeedia.vanning.dto.RowDto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service for importing planned work data from Excel files
 * This service handles Excel file processing, validation, and database
 * operations for importing planned work records with proper error handling.
 */
@Service
public class ExcelImportService {

	private final Logger log = LoggerFactory.getLogger(ExcelImportService.class);
	private final PlannedWorkRepository plannedWorkRepository;
	private final Validator validator;
	private final ExcelImportConfig config;
	private final TransactionalOperator transactionalOperator;
	private final ExcelStreamingReader excelStreamingReader;

	public ExcelImportService(PlannedWorkRepository plannedWorkRepository, Validator validator, ExcelImportConfig config
			, TransactionalOperator transactionalOperator, ExcelStreamingReader excelStreamingReader) {
		this.plannedWorkRepository = plannedWorkRepository;
		this.validator = validator;
		this.config = config;
		this.transactionalOperator = transactionalOperator;
		this.excelStreamingReader = excelStreamingReader;
	}

	/**
	 * Import planned work data from Excel file
	 */
	public Mono<ImportResultDto> importPlannedWorkFromExcel(FilePart filePart) {
		log.info("Starting Excel import for file: {}", filePart.filename());
		// Delegate to streaming implementation to keep public API stable
		return importPlannedWork(filePart)
				.doOnSuccess(result -> log.info("Excel import completed: {}", result))
				.doOnError(error -> log.error("Excel import failed: {}", error.getMessage()));
	}

	/**
	 * Stream rows from Excel using Apache POI SAX-based API.
	 */
	public Flux<RowDto> importExcelStreaming(FilePart filePart) {
		return DataBufferUtils.join(filePart.content())
				.flatMapMany(dataBuffer -> {
					byte[] bytes = new byte[dataBuffer.readableByteCount()];
					dataBuffer.read(bytes);
					DataBufferUtils.release(dataBuffer);

					return Flux.using(
							() -> new ByteArrayInputStream(bytes),
							excelStreamingReader::read,
							is -> { try { is.close(); } catch (Exception ignore) {} }
					);
				});
	}

	/**
	 * Streaming import that maps rows to PlannedWork and saves with per-row error capture.
	 */
	public Mono<ImportResultDto> importPlannedWork(FilePart filePart) {
		List<String> errors = new ArrayList<>();
		AtomicInteger total = new AtomicInteger(0);
		AtomicInteger saved = new AtomicInteger(0);

		return importExcelStreaming(filePart)
				.skip(2) // skip headers (two rows)
				.filter(this::isEmptyRowStreamingNegated)
				.map(this::toPlannedWork)
				.flatMap(result -> {
					if (!result.isValid) {
						errors.add("Sheet '" + result.sheetName + "' Row " + result.rowNum + ": " + result.errorMessage);
						return Mono.empty();
					}
					total.incrementAndGet();
					return Mono.just(new RowEntity(result.rowNum, result.sheetName, result.entity));
				})
				.buffer(100)
				.flatMap(batch -> Flux.fromIterable(batch)
						.flatMap(re -> plannedWorkRepository
								.findByWorkDateAndVanGpAndOperatorName(
										re.entity.getWorkDate(),
										re.entity.getVanGp(),
										re.entity.getOperatorName()
								)
								.next()
								.flatMap(existing -> {
									existing.setLoadTime(re.entity.getLoadTime());
									existing.setSize(re.entity.getSize());
									existing.setStartTime(re.entity.getStartTime());
									existing.setEndTime(re.entity.getEndTime());
									existing.setDuration(re.entity.getDuration());
									existing.setUpdatedAt(LocalDateTime.now());
									existing.setUpdatedBy("excel-import");
									return plannedWorkRepository.save(existing);
								})
								.switchIfEmpty(plannedWorkRepository.save(re.entity))
								.doOnSuccess(pw -> saved.incrementAndGet())
								.onErrorResume(e -> {
									errors.add("Sheet '" + re.sheetName + "' Row " + re.rowNum + ": " + mapDbError(e));
									return Mono.empty();
								})
								, 16)
						.then(Mono.empty())
				)
				.then(Mono.defer(() -> {
					int failed = errors.size();
					ImportResultDto dto = new ImportResultDto(total.get(), saved.get(), 0, failed, errors, List.of(),
							String.format("Import completed: %d saved, %d failed", saved.get(), failed));
					return Mono.just(dto);
				}))
				.doOnError(e -> log.error("Streaming import failed: {}", e.getMessage()));
	}

	private boolean isEmptyRowStreamingNegated(RowDto row) {
		if (row == null || row.cells() == null) {
			return false;
		}
		for (String c : row.cells()) {
			if (c != null && !c.trim().isEmpty()) {
				return true;
			}
		}
		return false;
	}

	/**
	 * Process Excel data from DataBuffer
	 */
	private Mono<ImportResultDto> processExcelData(DataBuffer dataBuffer) {
		return Mono.fromCallable(() -> {
					try (ByteArrayInputStream inputStream = new ByteArrayInputStream(dataBuffer.asByteBuffer().array());
							 Workbook workbook = new XSSFWorkbook(inputStream)) {
						return processWorkbook(workbook);
					} catch (IOException e) {
						throw new RuntimeException("Failed to process Excel file: " + e.getMessage(), e);
					} finally {
						DataBufferUtils.release(dataBuffer);
					}
				})
				.flatMap(result -> result)
				.subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic());
	}

	/**
	 * Process Excel workbook
	 */
	private Mono<ImportResultDto> processWorkbook(Workbook workbook) {
		return Mono.fromCallable(() -> {
			if (workbook.getNumberOfSheets() == 0) {
				throw new IllegalArgumentException("Excel file must contain at least one sheet");
			}

			List<String> errors = new ArrayList<>();
			List<String> warnings = new ArrayList<>();
			List<PlannedWork> allValidRecords = new ArrayList<>();
			int totalRecords = 0;

			// Process each sheet
			for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
				Sheet sheet = workbook.getSheetAt(sheetIndex);
				if (sheet == null) {
					warnings.add("Sheet " + (sheetIndex + 1) + " is empty, skipping");
					continue;
				}

				String sheetName = sheet.getSheetName();
				log.debug("Processing sheet: {} (index: {})", sheetName, sheetIndex);

				// Validate headers for this sheet
				if (!validateHeaders(sheet, errors, sheetName)) {
					continue; // Skip this sheet if headers are invalid
				}

				// Process data rows in this sheet using configuration
				List<PlannedWork> sheetValidRecords = new ArrayList<>();
				for (int rowIndex = config.getDataStartRowIndex(); rowIndex <= sheet.getLastRowNum(); rowIndex++) {
					Row row = sheet.getRow(rowIndex);
					if (row == null || isEmptyRow(row)) {
						continue;
					}

					totalRecords++;
					try {
						PlannedWork plannedWork = parseRowToPlannedWork(row, rowIndex + 1, sheetName);
						plannedWork.setOperatorName(sheetName);

						// Validate the entity
						Set<ConstraintViolation<PlannedWork>> violations = validator.validate(plannedWork);
						if (!violations.isEmpty()) {
							StringBuilder errorMsg = new StringBuilder("Sheet '" + sheetName + "' Row " + (rowIndex + 1) + ": ");
							for (ConstraintViolation<PlannedWork> violation : violations) {
								errorMsg.append(violation.getMessage()).append("; ");
							}
							errors.add(errorMsg.toString());
						} else {
							sheetValidRecords.add(plannedWork);
						}
					} catch (Exception e) {
						errors.add("Sheet '" + sheetName + "' Row " + (rowIndex + 1) + ": " + e.getMessage());
					}
				}

				allValidRecords.addAll(sheetValidRecords);
				log.debug("Processed sheet '{}': {} valid records", sheetName, sheetValidRecords.size());
			}

			if (allValidRecords.isEmpty()) {
				return new ImportResultDto(totalRecords, 0, 0, totalRecords, errors, warnings,
						"No valid records found to import");
			}

			// Deduplicate by unique business key to avoid redundant DB ops
			java.util.Map<String, PlannedWork> deduplicated = new java.util.LinkedHashMap<>();
			for (PlannedWork pw : allValidRecords) {
				String key = pw.getWorkDate() + "|" + pw.getVanGp() + "|" + pw.getOperatorName();
				// keep the last occurrence for a given key (or first; business rule dependent). Here we keep last.
				deduplicated.put(key, pw);
			}

			return new ProcessedWorkbookResult(
					new ImportResultDto(totalRecords, 0, 0, errors.size(), errors, warnings,
							"Records processed, ready for saving"),
					new java.util.ArrayList<>(deduplicated.values())
			);
		}).flatMap(result -> {
			if (result instanceof ImportResultDto) {
				// No valid records case
				return Mono.just((ImportResultDto) result);
			}
			if(result instanceof ProcessedWorkbookResult workbookResult) {
				return saveValidRecordsReactive(workbookResult.validRecords, workbookResult.importResult);
			}
			return Mono.error(new IllegalStateException("Unexpected result type"));
		});
	}

	private record ProcessedWorkbookResult(ImportResultDto importResult, List<PlannedWork> validRecords) {
	}

	/**
	 * Validate Excel headers
	 */
	private boolean validateHeaders(Sheet sheet, List<String> errors, String sheetName) {
		Row headerRow = sheet.getRow(config.getHeaderRowIndex());
		if (headerRow == null) {
			errors.add("Sheet '" + sheetName + "': Excel file must contain a header row at row " + (config.getHeaderRowIndex() + 1));
			return false;
		}

		// Validate each expected column header using centralized configuration
		for (ExcelColumnMapping mapping : ExcelColumnMapping.values()) {
			// Convert 1-based column index to 0-based for Excel cell access
			Cell cell = headerRow.getCell(mapping.getColumnIndex());
			String actualHeader = getCellValueAsString(cell).trim();

			if (!mapping.getHeaderName().equals(actualHeader)) {
				errors.add("Sheet '" + sheetName + "': Invalid header at column "
						+ (mapping.getColumnIndex() + 1) + ". Expected: '"
						+ mapping.getHeaderName() + "', Found: '" + actualHeader + "'");
				return false;
			}
		}

		return true;
	}

	/**
	 * Check if row is empty using centralized configuration
	 */
	private boolean isEmptyRow(Row row) {
		for (ExcelColumnMapping mapping : ExcelColumnMapping.values()) {
			// Convert 1-based column index to 0-based for Excel cell access
			Cell cell = row.getCell(mapping.getColumnIndex() - 1);
			if (cell != null && cell.getCellType() != CellType.BLANK) {
				String value = getCellValueAsString(cell).trim();
				if (!value.isEmpty()) {
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * Parse Excel row to PlannedWork entity
	 */
	private PlannedWork parseRowToPlannedWork(Row row, int rowNumber, String sheetName) {
		try {
			PlannedWork plannedWork = new PlannedWork();

			// Parse Date using column mapping
			String dateStr = getCellValueAsString(row.getCell(ExcelColumnMapping.DATE.getColumnIndex())).trim();
			plannedWork.setWorkDate(parseDate(dateStr, rowNumber, "Date"));

			// Parse Van GP using column mapping and configuration
			Cell vanGpCell = row.getCell(ExcelColumnMapping.VAN_GP.getColumnIndex());
			String vanGp = getCellValueAsString(vanGpCell).trim().toUpperCase();

			// Log for debugging
			log.debug("Row {}: Van GP cell at column {}, value: '{}'",
					rowNumber, ExcelColumnMapping.VAN_GP.getColumnIndex(), vanGp);

			if (vanGp.isEmpty() || vanGp.equals("0")) {
				throw new IllegalArgumentException("Van GP is required");
			}
			// Use configurable validation pattern
			if (!vanGp.matches(config.getValidation().getVanGpPattern())) {
				throw new IllegalArgumentException("Van GP must match pattern: " + config.getValidation().getVanGpPattern() + ", found: " + vanGp);
			}
			plannedWork.setVanGp(vanGp);

			// Set default values from configuration
			plannedWork.setDeliveryPlatform(config.getDefaultValues().getDeliveryPlatform());
			plannedWork.setCollectionPlatform(config.getDefaultValues().getCollectionPlatform());
			plannedWork.setSize(config.getDefaultValues().getSize());

			// Parse Load Time using column mapping
			String loadTimeStr = getCellValueAsString(row.getCell(ExcelColumnMapping.LOAD_TIME.getColumnIndex())).trim();
			log.debug("Row {}: Load Time cell at column {}, value: '{}'",
					rowNumber, ExcelColumnMapping.LOAD_TIME.getColumnIndex(), loadTimeStr);
			plannedWork.setLoadTime(parseTime(loadTimeStr, rowNumber, "Load Time"));

			// Parse Start Time using column mapping
			String startTimeStr = getCellValueAsString(row.getCell(ExcelColumnMapping.START_TIME.getColumnIndex())).trim();
			log.debug("Row {}: Start Time cell at column {}, value: '{}'",
					rowNumber, ExcelColumnMapping.START_TIME.getColumnIndex(), startTimeStr);
			plannedWork.setStartTime(parseTime(startTimeStr, rowNumber, "Start Time"));

			// Parse End Time using column mapping
			String endTimeStr = getCellValueAsString(row.getCell(ExcelColumnMapping.END_TIME.getColumnIndex())).trim();
			log.debug("Row {}: End Time cell at column {}, value: '{}'",
					rowNumber, ExcelColumnMapping.END_TIME.getColumnIndex(), endTimeStr);
			plannedWork.setEndTime(parseTime(endTimeStr, rowNumber, "End Time"));

			// Parse Duration using column mapping
			String durationStr = getCellValueAsString(row.getCell(ExcelColumnMapping.WORK_TIME.getColumnIndex())).trim();
			log.debug("Row {}: Duration cell at column {}, value: '{}'",
					rowNumber, ExcelColumnMapping.WORK_TIME.getColumnIndex(), durationStr);
			plannedWork.setDuration(parseTime(durationStr, rowNumber, "Duration"));

			// Set audit fields
			LocalDateTime now = LocalDateTime.now();
			plannedWork.setCreatedAt(now);
			plannedWork.setUpdatedAt(now);
			plannedWork.setCreatedBy("excel-import");
			plannedWork.setUpdatedBy("excel-import");

			return plannedWork;
		} catch (Exception e) {
			throw new RuntimeException("Failed to parse sheet '" + sheetName + "' row " + rowNumber + ": " + e.getMessage(), e);
		}
	}

	private RowMappingResult toPlannedWork(RowDto row) {
		try {
			// Map using ExcelColumnMapping indices assuming same positions as workbook mode
			PlannedWork plannedWork = new PlannedWork();
			String dateStr = safeCell(row, ExcelColumnMapping.DATE.getColumnIndex());
			plannedWork.setWorkDate(parseDate(dateStr, row.rowNum(), "Date"));

			String vanGp = safeCell(row, ExcelColumnMapping.VAN_GP.getColumnIndex()).trim().toUpperCase();
			if (vanGp.isEmpty() || vanGp.equals("0")) {
				throw new IllegalArgumentException("Van GP is required");
			}
			if (!vanGp.matches(config.getValidation().getVanGpPattern())) {
				throw new IllegalArgumentException("Van GP must match pattern: " + config.getValidation().getVanGpPattern());
			}
			plannedWork.setVanGp(vanGp);

			plannedWork.setOperatorName(row.sheetName());
			plannedWork.setDeliveryPlatform(config.getDefaultValues().getDeliveryPlatform());
			plannedWork.setCollectionPlatform(config.getDefaultValues().getCollectionPlatform());
			plannedWork.setSize(config.getDefaultValues().getSize());

			String loadTimeStr = safeCell(row, ExcelColumnMapping.LOAD_TIME.getColumnIndex());
			plannedWork.setLoadTime(parseTime(loadTimeStr, row.rowNum(), "Load Time"));

			String startTimeStr = safeCell(row, ExcelColumnMapping.START_TIME.getColumnIndex());
			plannedWork.setStartTime(parseTime(startTimeStr, row.rowNum(), "Start Time"));

			String endTimeStr = safeCell(row, ExcelColumnMapping.END_TIME.getColumnIndex());
			plannedWork.setEndTime(parseTime(endTimeStr, row.rowNum(), "End Time"));

			String durationStr = safeCell(row, ExcelColumnMapping.WORK_TIME.getColumnIndex());
			plannedWork.setDuration(parseTime(durationStr, row.rowNum(), "Duration"));

			LocalDateTime now = LocalDateTime.now();
			plannedWork.setCreatedAt(now);
			plannedWork.setUpdatedAt(now);
			plannedWork.setCreatedBy("excel-import");
			plannedWork.setUpdatedBy("excel-import");

			Set<ConstraintViolation<PlannedWork>> violations = validator.validate(plannedWork);
			if (!violations.isEmpty()) {
				StringBuilder sb = new StringBuilder();
				for (ConstraintViolation<PlannedWork> v : violations) {
					sb.append(v.getMessage()).append("; ");
				}
				return RowMappingResult.invalid(row.rowNum(), row.sheetName(), sb.toString());
			}

			return RowMappingResult.valid(row.rowNum(), row.sheetName(), plannedWork);
		} catch (Exception e) {
			return RowMappingResult.invalid(row.rowNum(), row.sheetName(), e.getMessage());
		}
	}

	private String safeCell(RowDto row, int index) {
		List<String> cells = row.cells();
		if (index < 0 || index >= cells.size()) {
			return "";
		}
		return cells.get(index) == null ? "" : cells.get(index);
	}

	private record RowEntity(int rowNum, String sheetName, PlannedWork entity) {}
	private static class RowMappingResult {
		final int rowNum;
		final String sheetName;
		final boolean isValid;
		final PlannedWork entity;
		final String errorMessage;

		private RowMappingResult(int rowNum, String sheetName, boolean isValid, PlannedWork entity, String errorMessage) {
			this.rowNum = rowNum;
			this.sheetName = sheetName;
			this.isValid = isValid;
			this.entity = entity;
			this.errorMessage = errorMessage;
		}

		static RowMappingResult valid(int rowNum, String sheetName, PlannedWork entity) {
			return new RowMappingResult(rowNum, sheetName, true, entity, null);
		}

		static RowMappingResult invalid(int rowNum, String sheetName, String errorMessage) {
			return new RowMappingResult(rowNum, sheetName, false, null, errorMessage);
		}
	}

	/**
	 * Get cell value as string
	 */
	private String getCellValueAsString(Cell cell) {
		if (cell == null) {
			return "";
		}

		switch (cell.getCellType()) {
			case STRING:
				return cell.getStringCellValue();
			case NUMERIC:
				if (DateUtil.isCellDateFormatted(cell)) {
					try {
						// Get the format string to determine if it's time or date
						String formatString = cell.getCellStyle().getDataFormatString().toLowerCase();
						double numericValue = cell.getNumericCellValue();

						// Check if this is a time-only format
						// Time formats typically have h, m, s and the numeric value is < 1 (fraction of a day)
						boolean strHasTime = formatString.contains("h") && formatString.contains("m");
						boolean strHasDate = formatString.contains("y") && formatString.contains("m") && formatString.contains("d");
						if (strHasTime && numericValue < 1.0) {
							// This is a time format - convert fraction of day to time
							LocalTime time = LocalTime.ofSecondOfDay((long) (numericValue * 24 * 60 * 60));
							return time.format(DateTimeFormatter.ofPattern("HH:mm"));
						} else if (strHasDate && strHasTime) {
							// This might be a datetime with time component
							LocalDateTime dateTime = cell.getLocalDateTimeCellValue();
							LocalTime time = dateTime.toLocalTime();
							return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
						} else {
							// This is a date format
							LocalDate date = cell.getLocalDateTimeCellValue().toLocalDate();
							return date.toString();
						}
					} catch (Exception e) {
						// Fallback to numeric value if date/time parsing fails
						log.warn("Failed to parse date/time cell value: {}, using numeric value", e.getMessage());
						return String.valueOf((long) cell.getNumericCellValue());
					}
				} else {
					return String.valueOf((long) cell.getNumericCellValue());
				}
			case BOOLEAN:
				return String.valueOf(cell.getBooleanCellValue());
			case FORMULA:
				return cell.getCellFormula();
			default:
				return "";
		}
	}

	/**
	 * Parse date string to LocalDate
	 */
	private LocalDate parseDate(String dateStr, int rowNumber, String fieldName) {
		if (dateStr == null || dateStr.trim().isEmpty()) {
			throw new IllegalArgumentException(fieldName + " is required");
		}

		try {
			// Try different date formats
			DateTimeFormatter[] formatters = {
				DateTimeFormatter.ofPattern("yyyy-MM-dd"),
				DateTimeFormatter.ofPattern("dd/MM/yyyy"),
				DateTimeFormatter.ofPattern("MM/dd/yyyy"),
				DateTimeFormatter.ofPattern("dd-MM-yyyy")
			};

			for (DateTimeFormatter formatter : formatters) {
				try {
					return LocalDate.parse(dateStr.trim(), formatter);
				} catch (DateTimeParseException ignored) {
					// Try next format
				}
			}

			throw new IllegalArgumentException("Invalid date format: " + dateStr);
		} catch (Exception e) {
			throw new IllegalArgumentException("Invalid " + fieldName + " format: " + dateStr);
		}
	}

	/**
	 * Parse time string to LocalTime
	 */
	private LocalTime parseTime(String timeStr, int rowNumber, String fieldName) {
		if (timeStr == null || timeStr.trim().isEmpty()) {
			throw new IllegalArgumentException(fieldName + " is required");
		}

		try {
			// Try different time formats
			DateTimeFormatter[] formatters = {
				DateTimeFormatter.ofPattern("HH:mm:ss"),
				DateTimeFormatter.ofPattern("HH:mm"),
				DateTimeFormatter.ofPattern("H:mm"),
				DateTimeFormatter.ofPattern("H:mm:ss")
			};

			for (DateTimeFormatter formatter : formatters) {
				try {
					return LocalTime.parse(timeStr.trim(), formatter);
				} catch (DateTimeParseException ignored) {
					// Try next format
				}
			}

			throw new IllegalArgumentException("Invalid time format: " + timeStr);
		} catch (Exception e) {
			throw new IllegalArgumentException("Invalid " + fieldName + " format: " + timeStr);
		}
	}

	/**
	 * Save valid records to database
	 */
	private ImportResultDto saveValidRecords(List<PlannedWork> validRecords, int totalRecords,
			List<String> errors, List<String> warnings) {
		int successfulInserts = 0;
		int successfulUpdates = 0;
		int failedRecords = errors.size();

		for (PlannedWork plannedWork : validRecords) {
			try {
				// Check if record exists (based on workDate, vanGp, deliveryPlatform, collectionPlatform)
				Flux<PlannedWork> existingRecords = plannedWorkRepository
						.findByWorkDateAndVanGpAndOperatorName(
								plannedWork.getWorkDate(),
								plannedWork.getVanGp(),
								plannedWork.getOperatorName()
						);

				PlannedWork existingRecord = existingRecords.blockFirst();

				if (existingRecord != null) {
					// Update existing record
					existingRecord.setLoadTime(plannedWork.getLoadTime());
					existingRecord.setSize(plannedWork.getSize());
					existingRecord.setStartTime(plannedWork.getStartTime());
					existingRecord.setEndTime(plannedWork.getEndTime());
					existingRecord.setDuration(plannedWork.getDuration());
					existingRecord.setUpdatedAt(LocalDateTime.now());
					existingRecord.setUpdatedBy("excel-import");

					plannedWorkRepository.save(existingRecord).block();
					successfulUpdates++;
				} else {
					// Insert new record
					plannedWorkRepository.save(plannedWork).block();
					successfulInserts++;
				}
			} catch (Exception e) {
				failedRecords++;
				errors.add("Failed to save record for date " + plannedWork.getWorkDate()
						+ ", Van GP " + plannedWork.getVanGp() + ": " + e.getMessage());
			}
		}

		String message = String.format("Import completed: %d inserted, %d updated, %d failed",
					successfulInserts, successfulUpdates, failedRecords);

		return new ImportResultDto(totalRecords, successfulInserts, successfulUpdates,
					failedRecords, errors, warnings, message);
	}

	private Mono<ImportResultDto> saveValidRecordsReactive(List<PlannedWork> validRecords, ImportResultDto partialResult) {
		record OpResult(String status, String message) {}

		return Flux.fromIterable(validRecords)
				// limit concurrency to avoid DB overload and preserve some ordering; adjust if needed
				.flatMap(plannedWork -> transactionalOperator.execute(status ->
								processSingleRecord(plannedWork)
									.map(s -> new OpResult(s, null))
									.onErrorResume(e -> Mono.just(new OpResult("failed", mapDbError(e))))
						)
						.single(),
						16 // concurrency
				)
				.collectList()
				.map(results -> {
					int inserts = (int) results.stream().filter(r -> "inserted".equals(r.status())).count();
					int updates = (int) results.stream().filter(r -> "updated".equals(r.status())).count();
					List<String> allErrors = new ArrayList<>(partialResult.getErrors());
					results.stream().filter(r -> "failed".equals(r.status()) && r.message() != null)
							.forEach(r -> allErrors.add(r.message()));

					int failed = allErrors.size();
					return new ImportResultDto(
								partialResult.getTotalRecords(),
								inserts,
								updates,
								failed,
								allErrors,
								partialResult.getWarnings(),
								String.format("Import completed: %d inserted, %d updated, %d failed", inserts, updates, failed)
					);
				});
	}

	private Mono<String> processSingleRecord(PlannedWork plannedWork) {
		return plannedWorkRepository
				.findByWorkDateAndVanGpAndOperatorName(
						plannedWork.getWorkDate(),
						plannedWork.getVanGp(),
						plannedWork.getOperatorName()
				)
				.next()
				.flatMap(existingRecord -> {
					existingRecord.setLoadTime(plannedWork.getLoadTime());
					existingRecord.setSize(plannedWork.getSize());
					existingRecord.setStartTime(plannedWork.getStartTime());
					existingRecord.setEndTime(plannedWork.getEndTime());
					existingRecord.setDuration(plannedWork.getDuration());
					existingRecord.setUpdatedAt(LocalDateTime.now());
					existingRecord.setUpdatedBy("excel-import");
					return plannedWorkRepository.save(existingRecord).thenReturn("updated");
				})
				.switchIfEmpty(
						Mono.defer(() -> plannedWorkRepository.save(plannedWork).thenReturn("inserted"))
				);
	}

	private String mapDbError(Throwable e) {
		String msg = e.getMessage() == null ? "Unknown database error" : e.getMessage();
		// Provide concise, user-friendly messages; could inspect vendor codes if needed
		if (e instanceof org.springframework.dao.DataIntegrityViolationException) {
			return "Constraint violation while saving record: " + msg;
		}
		if (e instanceof io.r2dbc.spi.R2dbcTransientResourceException) {
			return "Transient database error (retryable): " + msg;
		}
		if (e instanceof io.r2dbc.spi.R2dbcTimeoutException) {
			return "Database operation timed out: " + msg;
		}
		return "Database error: " + msg;
	}
}
