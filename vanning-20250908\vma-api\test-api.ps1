# Test Excel Import API
$baseUrl = "http://localhost:8080"
$excelFile = "test-plan.xlsx"

Write-Host "Testing Excel Import API..." -ForegroundColor Green
Write-Host "Excel file: $excelFile" -ForegroundColor Yellow

# First check if application is running
try {
    $health = Invoke-RestMethod -Uri "$baseUrl/actuator/health" -Method Get
    Write-Host "Application is running. Health status: $($health.status)" -ForegroundColor Green
} catch {
    Write-Host "Application is not running!" -ForegroundColor Red
    exit 1
}

# Test Excel import
try {
    Write-Host "Sending Excel file for import..." -ForegroundColor Yellow

    # Create credentials for basic authentication
    $username = "admin"
    $password = "admin123"
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $username, $password)))

    # Use Invoke-WebRequest for better error handling
    $headers = @{
        "Authorization" = "Basic $base64AuthInfo"
    }

    # Create form data manually
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"

    # Read file content
    $fileBytes = [System.IO.File]::ReadAllBytes($excelFile)
    $fileEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)

    # Create multipart body
    $bodyLines = (
        "--$boundary",
        "Content-Disposition: form-data; name=`"file`"; filename=`"test-plan.xlsx`"",
        "Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet$LF",
        $fileEnc,
        "--$boundary--$LF"
    ) -join $LF

    $response = Invoke-WebRequest -Uri "$baseUrl/api/planned-work/import" -Method Post -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary" -Headers $headers

    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Green
    Write-Host $response.Content -ForegroundColor White
    
} catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Yellow
        
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:" -ForegroundColor Yellow
            Write-Host $responseBody -ForegroundColor White
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Red
        }
    }
}
