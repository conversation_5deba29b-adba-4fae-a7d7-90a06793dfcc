{"groups": [{"name": "app", "type": "com.inspeedia.vanning.config.AppProperties", "sourceType": "com.inspeedia.vanning.config.AppProperties"}, {"name": "app.api", "type": "com.inspeedia.vanning.config.AppProperties$Api", "sourceType": "com.inspeedia.vanning.config.AppProperties", "sourceMethod": "getApi()"}, {"name": "app.cors", "type": "com.inspeedia.vanning.config.AppProperties$Cors", "sourceType": "com.inspeedia.vanning.config.AppProperties", "sourceMethod": "getCors()"}, {"name": "app.pagination", "type": "com.inspeedia.vanning.config.AppProperties$Pagination", "sourceType": "com.inspeedia.vanning.config.AppProperties", "sourceMethod": "getPagination()"}, {"name": "excel.import", "type": "com.inspeedia.vanning.config.ExcelImportConfig", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig"}, {"name": "excel.import.default-values", "type": "com.inspeedia.vanning.config.ExcelImportConfig$DefaultValues", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig", "sourceMethod": "getDefaultValues()"}, {"name": "excel.import.encoding", "type": "com.inspeedia.vanning.config.ExcelImportConfig$EncodingSettings", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig", "sourceMethod": "getEncoding()"}, {"name": "excel.import.validation", "type": "com.inspeedia.vanning.config.ExcelImportConfig$ValidationSettings", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig", "sourceMethod": "getValidation()"}], "properties": [{"name": "app.api.base-path", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.AppProperties$Api"}, {"name": "app.api.version", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.AppProperties$Api"}, {"name": "app.cors.allow-credentials", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.allowed-headers", "type": "java.util.List<java.lang.String>", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.allowed-methods", "type": "java.util.List<java.lang.String>", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.allowed-origins", "type": "java.util.List<java.lang.String>", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.max-age", "type": "java.lang.Long", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.pagination.default-page-size", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.AppProperties$Pagination"}, {"name": "app.pagination.max-page-size", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.AppProperties$Pagination"}, {"name": "excel.import.data-start-row-index", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig"}, {"name": "excel.import.default-values.collection-platform", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$DefaultValues"}, {"name": "excel.import.default-values.delivery-platform", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$DefaultValues"}, {"name": "excel.import.default-values.size", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$DefaultValues"}, {"name": "excel.import.encoding.default-charset", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$EncodingSettings"}, {"name": "excel.import.encoding.support-japanese", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$EncodingSettings"}, {"name": "excel.import.header-row-index", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig"}, {"name": "excel.import.validation.van-gp-max-length", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$ValidationSettings"}, {"name": "excel.import.validation.van-gp-min-length", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$ValidationSettings"}, {"name": "excel.import.validation.van-gp-pattern", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.ExcelImportConfig$ValidationSettings"}], "hints": [], "ignored": {"properties": []}}