import { App<PERSON><PERSON>, Dialog, Icon<PERSON>utton, <PERSON><PERSON><PERSON>, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import type { ReactNode } from 'react'

interface FullscreenModalProps {
	open: boolean
	title?: string
	onClose: () => void
	children: ReactNode
}

export default function FullscreenModal({ open, title, onClose, children }: FullscreenModalProps) {
	return (
		<Dialog fullScreen open={open} onClose={onClose}>
			<AppBar sx={{ position: 'relative' }}>
				<Toolbar>
					<IconButton edge="start" color="inherit" onClick={onClose} aria-label="close">
						<CloseIcon />
					</IconButton>
					{title ? (
						<Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
							{title}
						</Typography>
					) : null}
				</Toolbar>
			</AppBar>
			<div className="w-full h-full overflow-auto p-3 sm:p-4">{children}</div>
		</Dialog>
	)
}
