
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;

public class CreateTestExcel {

    public static void main(String[] args) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("南①");

            // Create header row (row 1, 0-based index 0)
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(1).setCellValue("date");
            headerRow.createCell(3).setCellValue("VANGP");
            headerRow.createCell(4).setCellValue("搬入時間");
            headerRow.createCell(5).setCellValue("開始時間");
            headerRow.createCell(6).setCellValue("終了時間");
            headerRow.createCell(7).setCellValue("作業時間");

            // Create data row (row 2, 0-based index 1)
            Row dataRow = sheet.createRow(1);

            // Date (column B, index 1)
            dataRow.createCell(1).setCellValue("2024-01-15");

            // Van GP (column D, index 3)
            dataRow.createCell(3).setCellValue("VG");

            // Load Time (column E, index 4) - 08:00
            Cell loadTimeCell = dataRow.createCell(4);
            loadTimeCell.setCellValue(LocalTime.of(8, 0).toSecondOfDay() / 86400.0); // Convert to Excel time format
            CellStyle timeStyle = workbook.createCellStyle();
            timeStyle.setDataFormat(workbook.createDataFormat().getFormat("h:mm"));
            loadTimeCell.setCellStyle(timeStyle);

            // Start Time (column F, index 5) - 08:30
            Cell startTimeCell = dataRow.createCell(5);
            startTimeCell.setCellValue(LocalTime.of(8, 30).toSecondOfDay() / 86400.0);
            startTimeCell.setCellStyle(timeStyle);

            // End Time (column G, index 6) - 16:30
            Cell endTimeCell = dataRow.createCell(6);
            endTimeCell.setCellValue(LocalTime.of(16, 30).toSecondOfDay() / 86400.0);
            endTimeCell.setCellStyle(timeStyle);

            // Work Time (column H, index 7) - 08:00
            Cell workTimeCell = dataRow.createCell(7);
            workTimeCell.setCellValue(LocalTime.of(8, 0).toSecondOfDay() / 86400.0);
            workTimeCell.setCellStyle(timeStyle);

            // Save the file
            try (FileOutputStream fileOut = new FileOutputStream("simple-test.xlsx")) {
                workbook.write(fileOut);
                System.out.println("Created simple-test.xlsx successfully!");
            }

        } catch (IOException e) {
            System.err.println("Error creating Excel file: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
