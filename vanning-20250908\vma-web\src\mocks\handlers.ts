import { http, HttpResponse } from 'msw'
import type { Task } from '../models/Task'

interface RandomStringOptions {
	length?: number
	secure?: boolean
}

function generateRandomUppercase(options: RandomStringOptions = {}): string {
	const { length = 2, secure = false } = options

	if (secure) {
		const array = new Uint32Array(length)
		window.crypto.getRandomValues(array)
		return Array.from(array, (value) => String.fromCharCode(65 + (value % 26))).join('')
	}

	return Array.from({ length }, () =>
		String.fromCharCode(65 + Math.floor(Math.random() * 26)),
	).join('')
}

function randomTime(baseHour: number) {
	const hour = baseHour + Math.floor(Math.random() * 3)
	const minute = Math.floor(Math.random() * 60)
	return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

function makeTasks(n: number): Task[] {
	return Array.from({ length: n }, (_, i) => {
		const shppingDate = new Date().toISOString().split('T')[0]
		const no = `${i + 1}`
		const vangp = generateRandomUppercase()
		const deliveryTime = randomTime(12)
		const plannedStart = randomTime(8)
		const plannedEnd = randomTime(12)
		const plannedDuration = `${Math.floor(Math.random() * 4 + 1)}h`
		const actualStart = randomTime(9)
		const actualEnd = randomTime(13)
		const actualDuration = `${Math.floor(Math.random() * 5 + 1)}h`
		const progress = Math.round(Math.random() * 100)
		return {
			id: i + 1,
			name: `User ${i + 1}`,
			no,
			shppingDate,
			vangp,
			deliveryTime,
			plannedStart,
			plannedEnd,
			plannedDuration,
			actualStart,
			actualEnd,
			actualDuration,
			progress,
		}
	})
}

export const handlers = [
	// Comment out the old tasks.json mock since we're now using the VMA API
	// http.get('/api/tasks.json', async () => {
	// 	const data = makeTasks(40)
	// 	return HttpResponse.json(data)
	// }),

	// Mock for the new VMA API tasks endpoint (fallback for development)
	http.get('/api/v1/tasks', async () => {
		const data = makeTasks(40)
		return HttpResponse.json(data)
	}),
]
