package com.inspeedia.vanning.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Task DTO representing a combined view of planned and actual work
 *
 * This DTO combines data from PlannedWork and ActualWork entities to provide
 * a unified task view for the frontend dashboard.
 */
public class TaskDto {

    private Long id;
    private String name;
    private String no;
    
    @JsonProperty("shppingDate")
    private String shippingDate;
    
    @JsonProperty("vangp")
    private String vanGp;
    
    private String deliveryTime;
    private String plannedStart;
    private String plannedEnd;
    private String plannedDuration;
    private String actualStart;
    private String actualEnd;
    private String actualDuration;
    private Integer progress;

    // Default constructor
    public TaskDto() {
    }

    // Constructor with all fields
    public TaskDto(Long id, String name, String no, String shippingDate, String vanGp,
                   String deliveryTime, String plannedStart, String plannedEnd, String plannedDuration,
                   String actualStart, String actualEnd, String actualDuration, Integer progress) {
        this.id = id;
        this.name = name;
        this.no = no;
        this.shippingDate = shippingDate;
        this.vanGp = vanGp;
        this.deliveryTime = deliveryTime;
        this.plannedStart = plannedStart;
        this.plannedEnd = plannedEnd;
        this.plannedDuration = plannedDuration;
        this.actualStart = actualStart;
        this.actualEnd = actualEnd;
        this.actualDuration = actualDuration;
        this.progress = progress;
    }

    // Getters
    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getNo() {
        return no;
    }

    public String getShippingDate() {
        return shippingDate;
    }

    public String getVanGp() {
        return vanGp;
    }

    public String getDeliveryTime() {
        return deliveryTime;
    }

    public String getPlannedStart() {
        return plannedStart;
    }

    public String getPlannedEnd() {
        return plannedEnd;
    }

    public String getPlannedDuration() {
        return plannedDuration;
    }

    public String getActualStart() {
        return actualStart;
    }

    public String getActualEnd() {
        return actualEnd;
    }

    public String getActualDuration() {
        return actualDuration;
    }

    public Integer getProgress() {
        return progress;
    }

    // Setters
    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public void setShippingDate(String shippingDate) {
        this.shippingDate = shippingDate;
    }

    public void setVanGp(String vanGp) {
        this.vanGp = vanGp;
    }

    public void setDeliveryTime(String deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public void setPlannedStart(String plannedStart) {
        this.plannedStart = plannedStart;
    }

    public void setPlannedEnd(String plannedEnd) {
        this.plannedEnd = plannedEnd;
    }

    public void setPlannedDuration(String plannedDuration) {
        this.plannedDuration = plannedDuration;
    }

    public void setActualStart(String actualStart) {
        this.actualStart = actualStart;
    }

    public void setActualEnd(String actualEnd) {
        this.actualEnd = actualEnd;
    }

    public void setActualDuration(String actualDuration) {
        this.actualDuration = actualDuration;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    @Override
    public String toString() {
        return "TaskDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", no='" + no + '\'' +
                ", shippingDate='" + shippingDate + '\'' +
                ", vanGp='" + vanGp + '\'' +
                ", deliveryTime='" + deliveryTime + '\'' +
                ", plannedStart='" + plannedStart + '\'' +
                ", plannedEnd='" + plannedEnd + '\'' +
                ", plannedDuration='" + plannedDuration + '\'' +
                ", actualStart='" + actualStart + '\'' +
                ", actualEnd='" + actualEnd + '\'' +
                ", actualDuration='" + actualDuration + '\'' +
                ", progress=" + progress +
                '}';
    }
}
