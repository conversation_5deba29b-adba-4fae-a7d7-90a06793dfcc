package com.inspeedia.vanning.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import com.inspeedia.vanning.config.ExcelColumnMapping;
import com.inspeedia.vanning.config.ExcelImportConfig;

/**
 * Service for generating Excel templates for planned work import
 *
 * This service creates downloadable Excel templates with proper headers and
 * sample data to guide users in preparing their import files.
 */
@Service
public class ExcelTemplateService {

    private final Logger log = LoggerFactory.getLogger(ExcelTemplateService.class);
    private final ExcelImportConfig config;

    public ExcelTemplateService(ExcelImportConfig config) {
        this.config = config;
    }

    /**
     * Generate Japanese headers array from configuration
     */
    private String[] getJapaneseHeaders() {
        // Full template headers including empty columns and additional sections
        return new String[]{
            "", // Column A (empty)
            ExcelColumnMapping.DATE.getHeaderName(), // Column B: date
            "出荷日", // Column C: Shipping Date (not used in import)
            ExcelColumnMapping.VAN_GP.getHeaderName(), // Column D: VANGP
            ExcelColumnMapping.LOAD_TIME.getHeaderName(), // Column E: 搬入時間
            ExcelColumnMapping.START_TIME.getHeaderName(), // Column F: 開始時間
            ExcelColumnMapping.END_TIME.getHeaderName(), // Column G: 終了時間
            ExcelColumnMapping.WORK_TIME.getHeaderName(), // Column H: 作業時間
            "VS", // Column I: VS (not used in import)
            "開始時間", // Column J: Start Time (actual work - not used)
            "終了時間", // Column K: End Time (actual work - not used)
            "作業時間", // Column L: Work Time (actual work - not used)
            "VS" // Column M: VS (actual work - not used)
        };
    }

    /**
     * Generate Excel template for planned work import (Japanese format)
     */
    public Resource generatePlannedWorkTemplate() {
        log.info("Generating planned work Excel template (Japanese format)");

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("User1"); // Sample sheet name

            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle sampleStyle = createSampleStyle(workbook);
            CellStyle titleStyle = createTitleStyle(workbook);

            // Create title row (row 1)
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(1);
            titleCell.setCellValue("南中①");
            titleCell.setCellStyle(titleStyle);

            // Create header row (row 2)
            String[] headers = getJapaneseHeaders();
            Row headerRow = sheet.createRow(config.getHeaderRowIndex());
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Create sample data rows
            addJapaneseSampleData(sheet, sampleStyle);

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
                // Add some extra width for better readability
                sheet.setColumnWidth(i, sheet.getColumnWidth(i) + 500);
            }

            // Freeze header row
            sheet.createFreezePane(0, 2);

            // Write to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("Successfully generated planned work Excel template (Japanese format)");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("Failed to generate Excel template: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate Excel template", e);
        }
    }

    /**
     * Create header cell style
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * Create sample data cell style
     */
    private CellStyle createSampleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setItalic(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    /**
     * Create title cell style
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * Add Japanese sample data rows
     */
    private void addJapaneseSampleData(Sheet sheet, CellStyle sampleStyle) {
        // Sample data rows
        String[][] sampleRows = {
            {"B1", "2025/9/5", "当日", "PR", "8:31", "8:36", "9:26", "0:50", "9:36", "", "", "", ""},
            {"B2", "2025/9/5", "当日", "OU", "9:31", "9:36", "10:26", "0:50", "10:36", "", "", "", ""},
            {"B3", "2025/9/5", "", "OU", "10:51", "10:56", "11:46", "0:50", "11:56", "", "", "", ""},
            {"B4", "2025/9/5", "当日", "II", "13:21", "13:26", "14:16", "0:50", "14:26", "", "", "", ""},
            {"B5", "2025/9/5", "泊り", "C", "14:21", "14:26", "15:16", "0:50", "15:26", "", "", "", ""},
            {"B6", "2025/9/5", "泊り", "PR", "15:21", "15:26", "16:16", "0:50", "16:26", "", "", "", ""},
            {"B7", "2025/9/5", "", "", "16:21", "16:26", "17:16", "0:50", "17:26", "", "", "", ""},
            {"B8", "2025/9/5", "", "", "17:20", "17:26", "18:17", "0:50", "18:26", "", "", "", ""},
            {"B9", "2025/9/5", "", "", "18:20", "18:26", "19:16", "0:50", "19:26", "", "", "", ""},
            {"B10", "2025/9/5", "泊り", "V3", "9:16", "", "", "0:50", "17:30", "", "", "", ""}
        };

        for (int rowIndex = 0; rowIndex < sampleRows.length; rowIndex++) {
            Row row = sheet.createRow(config.getDataStartRowIndex() + rowIndex);
            String[] rowData = sampleRows[rowIndex];

            for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
                Cell cell = row.createCell(colIndex);
                cell.setCellValue(rowData[colIndex]);
                cell.setCellStyle(sampleStyle);
            }
        }
    }
}
