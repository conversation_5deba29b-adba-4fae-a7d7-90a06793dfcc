package com.inspeedia.vanning.repository;

import com.inspeedia.vanning.domain.PlannedWork;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Repository interface for PlannedWork entity
 *
 * This repository provides reactive database operations for PlannedWork
 * entities using R2DBC with MSSQL database.
 */
@Repository
public interface PlannedWorkRepository extends R2dbcRepository<PlannedWork, Long> {

    /**
     * Find planned work by work date
     */
    Flux<PlannedWork> findByWorkDate(LocalDate workDate);

    /**
     * Find planned work by van GP
     */
    Flux<PlannedWork> findByVanGp(String vanGp);

    /**
     * Find planned work by delivery platform
     */
    Flux<PlannedWork> findByDeliveryPlatform(String deliveryPlatform);

    /**
     * Find planned work by collection platform
     */
    Flux<PlannedWork> findByCollectionPlatform(String collectionPlatform);

    /**
     * Find planned work by size
     */
    Flux<PlannedWork> findBySize(String size);

    /**
     * Find planned work by work date range
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND work_date BETWEEN :startDate AND :endDate ORDER BY work_date, start_time")
    Flux<PlannedWork> findByWorkDateBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find planned work by start time range
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND start_time BETWEEN :startTime AND :endTime ORDER BY start_time")
    Flux<PlannedWork> findByStartTimeBetween(@Param("startTime") LocalTime startTime, @Param("endTime") LocalTime endTime);

    /**
     * Find today's planned work
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND [date] = CAST(GETDATE() AS DATE) ORDER BY start_time")
    Flux<PlannedWork> findTodaysWork();

    /**
     * Find upcoming planned work (future dates)
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND date > CAST(GETDATE() AS DATE) ORDER BY date, start_time")
    Flux<PlannedWork> findUpcomingWork();

    /**
     * Find planned work by van GP and work date
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND van_gp = :vanGp AND work_date = :workDate ORDER BY start_time")
    Flux<PlannedWork> findByVanGpAndWorkDate(@Param("vanGp") String vanGp, @Param("workDate") LocalDate workDate);

    /**
     * Find planned work by platform combination
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND delivery_platform = :deliveryPlatform AND collection_platform = :collectionPlatform ORDER BY date, start_time")
    Flux<PlannedWork> findByPlatformCombination(@Param("deliveryPlatform") String deliveryPlatform, @Param("collectionPlatform") String collectionPlatform);

    /**
     * Find planned work by load time range
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND load_time BETWEEN :startTime AND :endTime ORDER BY load_time")
    Flux<PlannedWork> findByLoadTimeBetween(@Param("startTime") LocalTime startTime, @Param("endTime") LocalTime endTime);

    /**
     * Find planned work by duration range
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND duration BETWEEN :minDuration AND :maxDuration ORDER BY duration DESC")
    Flux<PlannedWork> findByDurationBetween(@Param("minDuration") LocalTime minDuration, @Param("maxDuration") LocalTime maxDuration);

    /**
     * Count planned work by van GP
     */
    @Query("SELECT COUNT(*) FROM planned_work WHERE deleted = 0 AND van_gp = :vanGp")
    Mono<Long> countByVanGp(@Param("vanGp") String vanGp);

    /**
     * Count planned work by date
     */
    @Query("SELECT COUNT(*) FROM planned_work WHERE deleted = 0 AND date = :date")
    Mono<Long> countByDate(@Param("date") LocalDate date);

    /**
     * Find planned work by size and work date range
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND size = :size AND work_date BETWEEN :startDate AND :endDate ORDER BY work_date, start_time")
    Flux<PlannedWork> findBySizeAndWorkDateBetween(@Param("size") String size, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Soft delete planned work by ID
     */
    @Query("UPDATE planned_work SET deleted = 1, updated_at = GETDATE() WHERE id = :id")
    Mono<Integer> softDeleteById(@Param("id") Long id);

    /**
     * Find all non-deleted planned work with pagination
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 ORDER BY work_date, start_time OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY")
    Flux<PlannedWork> findAllActive(@Param("offset") Long offset, @Param("limit") Integer limit);

    /**
     * Find planned work by unique combination of fields (for import duplicate
     * checking)
     */
    @Query("SELECT * FROM planned_work WHERE deleted = 0 AND work_date = :workDate AND van_gp = :vanGp AND operator_name = :operatorName")
    Flux<PlannedWork> findByWorkDateAndVanGpAndOperatorName(
            @Param("workDate") LocalDate workDate,
            @Param("vanGp") String vanGp,
            @Param("operatorName") String operatorName
    );
}
