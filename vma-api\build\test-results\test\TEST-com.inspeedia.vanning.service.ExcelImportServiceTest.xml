<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.ExcelImportServiceTest" tests="2" skipped="0" failures="0" errors="0" timestamp="2025-09-08T05:16:38" hostname="MSI" time="4.767">
  <properties/>
  <testcase name="testImportPlannedWorkFromExcel_InvalidFile()" classname="com.inspeedia.vanning.service.ExcelImportServiceTest" time="4.61"/>
  <testcase name="testImportPlannedWorkFromExcel_Success()" classname="com.inspeedia.vanning.service.ExcelImportServiceTest" time="0.152"/>
  <system-out><![CDATA[10:46:43.148 [Test worker] INFO com.inspeedia.vanning.service.ExcelImportService -- Starting Excel import for file: 計画.xlsx
10:46:43.535 [Test worker] ERROR com.inspeedia.vanning.service.ExcelImportService -- Streaming import failed: Invalid file
10:46:43.536 [Test worker] ERROR com.inspeedia.vanning.service.ExcelImportService -- Excel import failed: Invalid file
10:46:43.673 [Test worker] INFO com.inspeedia.vanning.service.ExcelImportService -- Starting Excel import for file: 計画.xlsx
10:46:43.699 [Test worker] INFO com.inspeedia.vanning.service.ExcelImportService -- Excel import completed: ImportResultDto{totalRecords=0, successfulInserts=0, successfulUpdates=0, failedRecords=0, errors=[], warnings=[], message='Import completed: 0 saved, 0 failed'}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
