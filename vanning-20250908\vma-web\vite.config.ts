import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { copyFileSync, mkdirSync } from 'fs'
import { resolve } from 'path'

// Plugin to copy locale files during build
function copyLocalesPlugin() {
	return {
		name: 'copy-locales',
		writeBundle() {
			// Ensure the locales directory exists in dist
			mkdirSync('dist/locales', { recursive: true })

			// Copy locale files from source to dist
			copyFileSync(resolve('src/core/i18n/locales/en.json'), resolve('dist/locales/en.json'))
			copyFileSync(resolve('src/core/i18n/locales/ja.json'), resolve('dist/locales/ja.json'))

			console.log('✓ Locale files copied to dist/locales/')
		},
	}
}

// https://vite.dev/config/
export default defineConfig({
	plugins: [react(), copyLocalesPlugin()],
	publicDir: 'public',
	build: {
		outDir: 'dist',
		assetsDir: 'assets',
	},
	server: {
		// Serve locale files during development
		fs: {
			allow: ['..'],
		},
		proxy: {
			'/api/v1': {
				target: 'http://localhost:8080',
				changeOrigin: true,
				secure: false,
			},
		},
	},
})
