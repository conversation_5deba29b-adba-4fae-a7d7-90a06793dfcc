# Excel Import Configuration Guide

This document explains the centralized configuration system for Excel import functionality, designed to handle Japanese characters and make column mappings easily maintainable.

## Overview

The Excel import system has been refactored to use a centralized configuration approach that eliminates hardcoded values and makes the system more maintainable and flexible.

## Key Components

### 1. ExcelColumnMapping Enum (`src/main/java/com/inspeedia/vanning/config/ExcelColumnMapping.java`)

This enum defines all Excel column mappings in one central location:

```java
public enum ExcelColumnMapping {
    DATE(1, "date", "date", true),
    VAN_GP(3, "VANGP", "vanGp", true),
    LOAD_TIME(4, "搬入時間", "loadTime", true),
    START_TIME(5, "開始時間", "startTime", true),
    END_TIME(6, "終了時間", "endTime", true),
    WORK_TIME(7, "作業時間", "duration", true);
}
```

**Parameters:**
- `columnIndex`: 0-based Excel column index
- `headerName`: Expected header name (supports Japanese characters)
- `fieldName`: Corresponding PlannedWork entity field name
- `required`: Whether the column is required

### 2. ExcelImportConfig Class (`src/main/java/com/inspeedia/vanning/config/ExcelImportConfig.java`)

Configuration class with nested settings for different aspects:

```java
@Configuration
@ConfigurationProperties(prefix = "excel.import")
public class ExcelImportConfig {
    private int headerRowIndex = 1;        // Row where headers are located
    private int dataStartRowIndex = 2;     // Row where data starts
    private DefaultValues defaultValues;   // Default values for missing fields
    private ValidationSettings validation; // Validation patterns and rules
    private EncodingSettings encoding;     // Character encoding settings
}
```

### 3. Configuration Properties (`src/main/resources/excel-import.properties`)

External configuration file for easy modification:

```properties
# Row Configuration
excel.import.header-row-index=1
excel.import.data-start-row-index=2

# Default Values
excel.import.default-values.delivery-platform=A
excel.import.default-values.collection-platform=B
excel.import.default-values.size=L1

# Validation Settings
excel.import.validation.van-gp-pattern=^[A-Z0-9]{1,3}$
excel.import.validation.van-gp-min-length=1
excel.import.validation.van-gp-max-length=3

# Character Encoding
excel.import.encoding.default-charset=UTF-8
excel.import.encoding.support-japanese=true
```

## Benefits

### 1. **Centralized Management**
- All column mappings in one enum
- All configuration settings in one place
- Easy to modify without touching business logic

### 2. **Japanese Character Support**
- Proper UTF-8 encoding configuration
- Japanese headers handled correctly
- No hardcoded Japanese strings in business logic

### 3. **Maintainability**
- Change column indices in one place
- Modify validation rules externally
- Add new columns easily

### 4. **Flexibility**
- Configurable row positions
- Adjustable validation patterns
- Customizable default values

## Usage Examples

### Adding a New Column

1. **Add to ExcelColumnMapping enum:**
```java
NEW_FIELD(8, "新しいフィールド", "newField", false)
```

2. **Update PlannedWork entity** (if needed)

3. **Update parsing logic** in ExcelImportService (automatically uses the enum)

### Changing Column Position

Simply update the column index in the enum:
```java
VAN_GP(5, "VANGP", "vanGp", true) // Changed from index 3 to 5
```

### Modifying Validation Rules

Update the properties file:
```properties
excel.import.validation.van-gp-pattern=^[A-Z0-9]{1,5}$
excel.import.validation.van-gp-max-length=5
```

### Changing Default Values

Update the properties file:
```properties
excel.import.default-values.delivery-platform=C
excel.import.default-values.collection-platform=D
```

## File Structure

```
src/main/java/com/inspeedia/vanning/config/
├── ExcelColumnMapping.java      # Column definitions
└── ExcelImportConfig.java       # Configuration class

src/main/resources/
├── excel-import.properties      # External configuration
└── application.yml              # Imports excel-import.properties

src/main/java/com/inspeedia/vanning/service/
├── ExcelImportService.java      # Uses centralized config
└── ExcelTemplateService.java    # Uses centralized config
```

## Migration from Hardcoded Values

### Before (Hardcoded):
```java
private static final String[] EXPECTED_HEADERS = {
    "date", "VANGP", "搬入時間", "開始時間", "終了時間", "作業時間"
};

String vanGp = getCellValueAsString(row.getCell(3)).trim(); // Hardcoded index
```

### After (Centralized):
```java
String vanGp = getCellValueAsString(
    row.getCell(ExcelColumnMapping.VAN_GP.getColumnIndex())
).trim();
```

## Error Handling

The system provides detailed error messages with:
- Sheet name
- Column position (human-readable)
- Expected vs actual header names
- Row numbers for data errors

## Testing

When adding new configurations:
1. Update unit tests to use the new enum values
2. Test with actual Japanese Excel files
3. Verify error messages are clear and helpful
4. Test template generation with new configurations

## Future Enhancements

The centralized system makes it easy to add:
- Multiple Excel formats support
- Dynamic column mapping from database
- User-configurable column positions
- Multi-language header support
- Custom validation rules per column
