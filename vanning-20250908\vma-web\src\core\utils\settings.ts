/**
 * Settings utility functions for managing app preferences
 */

export interface AppSettings {
	locale: 'en' | 'ja'
	themeMode: 'light' | 'dark'
	fontScale: number
}

// Default settings
export const DEFAULT_SETTINGS: AppSettings = {
	locale: 'en',
	themeMode: 'light',
	fontScale: 1,
}

// Settings validation
export const validateSettings = (settings: Partial<AppSettings>): AppSettings => {
	return {
		locale: settings.locale && ['en', 'ja'].includes(settings.locale) ? settings.locale : DEFAULT_SETTINGS.locale,
		themeMode: settings.themeMode && ['light', 'dark'].includes(settings.themeMode) ? settings.themeMode : DEFAULT_SETTINGS.themeMode,
		fontScale: typeof settings.fontScale === 'number' && settings.fontScale >= 0.85 && settings.fontScale <= 1.3 
			? settings.fontScale 
			: DEFAULT_SETTINGS.fontScale,
	}
}

// Export settings to JSON
export const exportSettings = (settings: AppSettings): string => {
	try {
		return JSON.stringify(settings, null, 2)
	} catch (error) {
		console.error('Failed to export settings:', error)
		throw new Error('Failed to export settings')
	}
}

// Import settings from JSON
export const importSettings = (jsonString: string): AppSettings => {
	try {
		const parsed = JSON.parse(jsonString)
		return validateSettings(parsed)
	} catch (error) {
		console.error('Failed to import settings:', error)
		throw new Error('Invalid settings format')
	}
}

// Reset settings to defaults
export const resetSettings = (): AppSettings => {
	return { ...DEFAULT_SETTINGS }
}
