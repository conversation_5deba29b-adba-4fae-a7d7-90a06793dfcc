/**
 * Tests for HTTP client utilities
 */

import { httpGet, httpPost } from '../httpClient'

// Mock fetch globally
;(globalThis as any).fetch = jest.fn()

const mockFetch = fetch as jest.MockedFunction<typeof fetch>

describe('HTTP Client', () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('httpGet', () => {
		it('should make successful GET request', async () => {
			// Arrange
			const mockResponse = { data: 'test data' }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act
			const result = await httpGet<typeof mockResponse>('/api/test')

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/test', {})
			expect(mockFetch).toHaveBeenCalledTimes(1)
		})

		it('should handle request with custom options', async () => {
			// Arrange
			const mockResponse = { data: 'test data' }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const options = {
				headers: { Authorization: 'Bearer token' },
				method: 'GET',
			}

			// Act
			const result = await httpGet<typeof mockResponse>('/api/test', options)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/test', options)
		})

		it('should handle request with abort signal', async () => {
			// Arrange
			const mockResponse = { data: 'test data' }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const abortController = new AbortController()
			const signal = abortController.signal

			// Act
			const result = await httpGet<typeof mockResponse>('/api/test', { signal })

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/test', { signal })
		})

		it('should handle request with both signal and headers', async () => {
			// Arrange
			const mockResponse = { data: 'test data' }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const abortController = new AbortController()
			const signal = abortController.signal
			const headers = { 'Content-Type': 'application/json' }

			// Act
			const result = await httpGet<typeof mockResponse>('/api/test', { signal, headers })

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/test', { signal, headers })
		})

		it('should throw error when response is not ok', async () => {
			// Arrange
			const mockResponseObject = {
				ok: false,
				status: 404,
				statusText: 'Not Found',
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act & Assert
			await expect(httpGet('/api/test')).rejects.toThrow('HTTP 404')
			expect(mockFetch).toHaveBeenCalledWith('/api/test', {})
		})

		it('should throw error for 500 status', async () => {
			// Arrange
			const mockResponseObject = {
				ok: false,
				status: 500,
				statusText: 'Internal Server Error',
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act & Assert
			await expect(httpGet('/api/test')).rejects.toThrow('HTTP 500')
		})

		it('should throw error for 403 status', async () => {
			// Arrange
			const mockResponseObject = {
				ok: false,
				status: 403,
				statusText: 'Forbidden',
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act & Assert
			await expect(httpGet('/api/test')).rejects.toThrow('HTTP 403')
		})

		it('should handle network errors', async () => {
			// Arrange
			const networkError = new Error('Network error')
			mockFetch.mockRejectedValue(networkError)

			// Act & Assert
			await expect(httpGet('/api/test')).rejects.toThrow('Network error')
		})

		it('should handle JSON parsing errors', async () => {
			// Arrange
			const mockResponseObject = {
				ok: true,
				json: () => Promise.reject(new Error('Invalid JSON')),
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act & Assert
			await expect(httpGet('/api/test')).rejects.toThrow('Invalid JSON')
		})

		it('should handle empty response', async () => {
			// Arrange
			const mockResponse = null
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act
			const result = await httpGet<null>('/api/test')

			// Assert
			expect(result).toBeNull()
		})

		it('should handle array response', async () => {
			// Arrange
			const mockResponse = [{ id: 1 }, { id: 2 }]
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act
			const result = await httpGet<typeof mockResponse>('/api/test')

			// Assert
			expect(result).toEqual(mockResponse)
			expect(Array.isArray(result)).toBe(true)
		})

		it('should handle string response', async () => {
			// Arrange
			const mockResponse = 'test string'
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act
			const result = await httpGet<string>('/api/test')

			// Assert
			expect(result).toBe('test string')
			expect(typeof result).toBe('string')
		})

		it('should handle number response', async () => {
			// Arrange
			const mockResponse = 42
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act
			const result = await httpGet<number>('/api/test')

			// Assert
			expect(result).toBe(42)
			expect(typeof result).toBe('number')
		})
	})

	describe('error handling edge cases', () => {
		it('should handle fetch timeout', async () => {
			// Arrange
			mockFetch.mockImplementation(
				() =>
					new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100)),
			)

			// Act & Assert
			await expect(httpGet('/api/test')).rejects.toThrow('Timeout')
		})

		it('should handle aborted request', async () => {
			// Arrange
			const abortController = new AbortController()
			const signal = abortController.signal

			mockFetch.mockImplementation(
				() =>
					new Promise((_, reject) => {
						signal.addEventListener('abort', () => reject(new Error('Aborted')))
					}),
			)

			// Act
			const requestPromise = httpGet('/api/test', { signal })
			abortController.abort()

			// Assert
			await expect(requestPromise).rejects.toThrow('Aborted')
		})
	})

	describe('httpPost', () => {
		it('should make successful POST request with FormData', async () => {
			// Arrange
			const mockResponse = { success: true, message: 'Upload successful' }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const formData = new FormData()
			formData.append('file', new File(['test'], 'test.txt'))

			// Act
			const result = await httpPost<typeof mockResponse>('/api/upload', formData)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/upload', {
				method: 'POST',
				headers: {},
				body: formData,
			})
		})

		it('should make successful POST request with JSON object', async () => {
			// Arrange
			const mockResponse = { id: 1, name: 'Test' }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const requestBody = { name: 'Test', value: 123 }

			// Act
			const result = await httpPost<typeof mockResponse>('/api/create', requestBody)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/create', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			})
		})

		it('should make successful POST request with string body', async () => {
			// Arrange
			const mockResponse = { success: true }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const requestBody = 'plain text data'

			// Act
			const result = await httpPost<typeof mockResponse>('/api/text', requestBody)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/text', {
				method: 'POST',
				headers: { 'Content-Type': 'text/plain' },
				body: requestBody,
			})
		})

		it('should handle POST request with abort signal', async () => {
			// Arrange
			const mockResponse = { success: true }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			const abortController = new AbortController()
			const signal = abortController.signal
			const requestBody = { test: 'data' }

			// Act
			const result = await httpPost<typeof mockResponse>('/api/test', requestBody, { signal })

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/test', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
				signal,
			})
		})

		it('should throw error when POST response is not ok', async () => {
			// Arrange
			const mockResponseObject = {
				ok: false,
				status: 400,
				statusText: 'Bad Request',
				text: jest.fn().mockResolvedValue('Bad Request'),
			} as unknown as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act & Assert
			await expect(httpPost('/api/test', { data: 'test' })).rejects.toThrow('HTTP 400')
		})

		it('should handle POST request without body', async () => {
			// Arrange
			const mockResponse = { success: true }
			const mockJsonPromise = Promise.resolve(mockResponse)
			const mockResponseObject = {
				ok: true,
				json: () => mockJsonPromise,
			} as Response

			mockFetch.mockResolvedValue(mockResponseObject)

			// Act
			const result = await httpPost<typeof mockResponse>('/api/test')

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith('/api/test', {
				method: 'POST',
				headers: {},
				body: undefined,
			})
		})
	})
})
