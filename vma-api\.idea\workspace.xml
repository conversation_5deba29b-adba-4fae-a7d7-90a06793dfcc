<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8f4ea853-3c42-4503-bdc0-6c115699458c" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/config/ExcelImportConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/config/ExcelImportConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/controller/PlannedWorkController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/controller/PlannedWorkController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/controller/TaskController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/controller/TaskController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/repository/ActualWorkRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/repository/ActualWorkRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/repository/PlannedWorkRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/repository/PlannedWorkRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/service/ActualWorkService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/service/ActualWorkService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/service/PlannedWorkService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/service/PlannedWorkService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/service/TaskService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/inspeedia/vanning/service/TaskService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/inspeedia/vanning/service/ExcelImportServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/inspeedia/vanning/service/ExcelImportServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/inspeedia/vanning/service/TaskServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/inspeedia/vanning/service/TaskServiceTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="vma-api" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="vma-api" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="vma-api" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="build" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="31xDWaERG6A6ETp1ou8lYvF5IYI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Gradle.Build vma-api.executor": "Run",
    "Gradle.vma-api [:com.inspeedia.vanning.VmApplication.main()].executor": "Debug",
    "Gradle.vma-api [clean].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true"
  }
}]]></component>
  <component name="RunManager" selected="Application.VmApplication">
    <configuration name="VmApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.inspeedia.vanning.VmApplication" />
      <module name="vma-api.main" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.inspeedia.vanning.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="vma-api [clean]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="clean" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.vma-api [clean]" />
        <item itemvalue="Application.VmApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8f4ea853-3c42-4503-bdc0-6c115699458c" name="Changes" comment="" />
      <created>1756456864773</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756456864773</updated>
    </task>
    <servers />
  </component>
</project>