# Development Debugging Guide for Planned Work Import

## Issues Fixed

### 1. ✅ API Configuration Issue
**Problem**: Empty `API_BASE_URL` causing confusion about request routing.
**Solution**: 
- Enhanced environment configuration with proper fallbacks
- Added debug logging to show which URL is being used
- Created `.env.development` file for easy configuration

### 2. ✅ CORS Configuration Conflict
**Problem**: Two conflicting CORS configurations in backend.
**Solution**:
- Removed CORS configuration from `SecurityConfig.java`
- Let `WebConfig.java` handle CORS using `application.yml` settings
- Added planned work endpoints to security whitelist

### 3. ✅ Authorization Issue
**Problem**: Planned work import endpoint was not whitelisted in security config.
**Solution**: Added `/api/v1/planned-work/**` to permitted paths

### 4. ✅ Proxy Configuration
**Problem**: No visibility into proxy behavior.
**Solution**: Added detailed logging for proxy requests and responses

## How to Debug in Development

### Step 1: Enable Debug Logging
Edit `vma-web/.env.development`:
```env
VITE_DEBUG_HTTP=true
```

### Step 2: Check Console Logs
Open browser DevTools → Console and look for:
- `[HTTP Client]` messages showing URL resolution
- `[Vite Proxy]` messages showing proxy behavior
- Network requests in the Network tab

### Step 3: Verify Backend is Running
Ensure the Spring Boot backend is running on `http://localhost:8080`

### Step 4: Test the Import
1. Go to Settings page
2. Upload an Excel file
3. Watch console logs for request flow

## Expected Log Flow

### Successful Request:
```
[HTTP Client] Using relative URL for proxy: /api/v1/planned-work/import
[HTTP Client] POST /api/v1/planned-work/import with FormData (file upload)
[Vite Proxy] POST /api/v1/planned-work/import -> http://localhost:8080/api/v1/planned-work/import
[Vite Proxy] POST /api/v1/planned-work/import <- 200
[HTTP Client] Response: 200 OK
```

### Failed Request (Backend Down):
```
[HTTP Client] Using relative URL for proxy: /api/v1/planned-work/import
[HTTP Client] POST /api/v1/planned-work/import with FormData (file upload)
Proxy error: Error: connect ECONNREFUSED 127.0.0.1:8080
[HTTP Client] Request failed: TypeError: Failed to fetch
```

## Troubleshooting Common Issues

### Issue: "Failed to fetch" Error
**Cause**: Backend not running or wrong port
**Solution**: 
1. Start backend: `./gradlew bootRun` in vma-api directory
2. Verify it's running on port 8080
3. Check backend logs for startup errors

### Issue: CORS Error
**Cause**: Backend CORS configuration issue
**Solution**: 
1. Check backend logs for CORS configuration messages
2. Verify `http://localhost:5173` is in allowed origins
3. Restart backend after configuration changes

### Issue: 401 Unauthorized
**Cause**: Endpoint not whitelisted in security config
**Solution**: Verify `/api/v1/planned-work/**` is in permitted paths

### Issue: 404 Not Found
**Cause**: Wrong API endpoint or backend not running
**Solution**: 
1. Check if backend is running
2. Verify endpoint exists: `http://localhost:8080/api/v1/planned-work/import`
3. Check backend controller mapping

## Alternative Configuration for Direct Backend Access

If proxy is causing issues, you can bypass it:

Edit `vma-web/.env.development`:
```env
VITE_API_BASE_URL=http://localhost:8080
VITE_DEBUG_HTTP=true
```

This will make requests directly to the backend, bypassing the Vite proxy.

## Backend Configuration Verification

Ensure these settings in `vma-api/src/main/resources/application.yml`:
```yaml
app:
  cors:
    allowed-origins:
      - "http://localhost:5173"  # Vite dev server
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers:
      - "*"
    allow-credentials: true
```

## Testing the Fix

1. Start backend: `cd vma-api && ./gradlew bootRun`
2. Start frontend: `cd vma-web && npm run dev`
3. Open `http://localhost:5173`
4. Go to Settings page
5. Try uploading an Excel file
6. Check console logs for request flow

The import should now work without CORS or authorization errors.
