package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

/**
 * Unit tests for PlannedWorkService
 */
@ExtendWith(MockitoExtension.class)
class PlannedWorkServiceTest {

    @Mock
    private PlannedWorkRepository plannedWorkRepository;

    @InjectMocks
    private PlannedWorkService plannedWorkService;

    private PlannedWork plannedWork;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        plannedWork = new PlannedWork();
        plannedWork.setId(1L);
        plannedWork.setWorkDate(LocalDate.of(2024, 1, 15));
        plannedWork.setOperatorName("田中太郎");
        plannedWork.setVanGp("AB");
        plannedWork.setDeliveryPlatform("A");
        plannedWork.setCollectionPlatform("B");
        plannedWork.setLoadTime(LocalTime.of(7, 30));
        plannedWork.setSize("L1");
        plannedWork.setStartTime(LocalTime.of(8, 0));
        plannedWork.setEndTime(LocalTime.of(17, 0));
        plannedWork.setDuration(LocalTime.of(9, 0));
    }

    @Test
    void testCreatePlannedWork() {
        when(plannedWorkRepository.save(any(PlannedWork.class)))
                .thenReturn(Mono.just(plannedWork));

        PlannedWork newWork = new PlannedWork(LocalDate.of(2024, 1, 15), "田中太郎", "AB", "A", "B",
                LocalTime.of(7, 30), "L1", LocalTime.of(8, 0), LocalTime.of(17, 0), LocalTime.of(9, 0));

        StepVerifier.create(plannedWorkService.createPlannedWork(newWork))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).save(any(PlannedWork.class));
    }

    @Test
    void testUpdatePlannedWork() {
        when(plannedWorkRepository.findById(1L))
                .thenReturn(Mono.just(plannedWork));
        when(plannedWorkRepository.save(any(PlannedWork.class)))
                .thenReturn(Mono.just(plannedWork));

        PlannedWork updateData = new PlannedWork(LocalDate.of(2024, 2, 20), "佐藤花子", "CD", "C", "D",
                LocalTime.of(8, 0), "M2", LocalTime.of(9, 0), LocalTime.of(18, 0), LocalTime.of(9, 0));

        StepVerifier.create(plannedWorkService.updatePlannedWork(1L, updateData))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findById(1L);
        verify(plannedWorkRepository).save(any(PlannedWork.class));
    }

    @Test
    void testUpdatePlannedWorkNotFound() {
        when(plannedWorkRepository.findById(999L))
                .thenReturn(Mono.empty());

        PlannedWork updateData = new PlannedWork(LocalDate.of(2024, 2, 20), "佐藤花子", "CD", "C", "D",
                LocalTime.of(8, 0), "M2", LocalTime.of(9, 0), LocalTime.of(18, 0), LocalTime.of(9, 0));

        StepVerifier.create(plannedWorkService.updatePlannedWork(999L, updateData))
                .expectError(IllegalArgumentException.class)
                .verify();

        verify(plannedWorkRepository).findById(999L);
    }

    @Test
    void testGetPlannedWorkById() {
        when(plannedWorkRepository.findById(1L))
                .thenReturn(Mono.just(plannedWork));

        StepVerifier.create(plannedWorkService.getPlannedWorkById(1L))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findById(1L);
    }

    @Test
    void testGetPlannedWorkByIdNotFound() {
        when(plannedWorkRepository.findById(999L))
                .thenReturn(Mono.empty());

        StepVerifier.create(plannedWorkService.getPlannedWorkById(999L))
                .expectError(IllegalArgumentException.class)
                .verify();

        verify(plannedWorkRepository).findById(999L);
    }

    @Test
    void testGetAllActivePlannedWork() {
        PlannedWork work1 = new PlannedWork(LocalDate.of(2024, 1, 15), "田中太郎", "AB", "A", "B",
                LocalTime.of(7, 30), "L1", LocalTime.of(8, 0), LocalTime.of(17, 0), LocalTime.of(9, 0));
        PlannedWork work2 = new PlannedWork(LocalDate.of(2024, 1, 16), "佐藤花子", "CD", "C", "D",
                LocalTime.of(8, 0), "M2", LocalTime.of(9, 0), LocalTime.of(18, 0), LocalTime.of(9, 0));

        when(plannedWorkRepository.findAllActive(0L, 20))
                .thenReturn(Flux.just(work1, work2));

        StepVerifier.create(plannedWorkService.getAllActivePlannedWork(0, 20))
                .expectNext(work1)
                .expectNext(work2)
                .verifyComplete();

        verify(plannedWorkRepository).findAllActive(0L, 20);
    }

    @Test
    void testGetPlannedWorkByDate() {
        LocalDate testDate = LocalDate.of(2024, 1, 15);
        when(plannedWorkRepository.findByWorkDate(testDate))
                .thenReturn(Flux.just(plannedWork));

        StepVerifier.create(plannedWorkService.getPlannedWorkByDate(testDate))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findByWorkDate(testDate);
    }

    @Test
    void testGetPlannedWorkByVanGp() {
        when(plannedWorkRepository.findByVanGp("AB"))
                .thenReturn(Flux.just(plannedWork));

        StepVerifier.create(plannedWorkService.getPlannedWorkByVanGp("AB"))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findByVanGp("AB");
    }

    @Test
    void testGetPlannedWorkByDeliveryPlatform() {
        when(plannedWorkRepository.findByDeliveryPlatform("A"))
                .thenReturn(Flux.just(plannedWork));

        StepVerifier.create(plannedWorkService.getPlannedWorkByDeliveryPlatform("A"))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findByDeliveryPlatform("A");
    }

    @Test
    void testGetPlannedWorkByCollectionPlatform() {
        when(plannedWorkRepository.findByCollectionPlatform("B"))
                .thenReturn(Flux.just(plannedWork));

        StepVerifier.create(plannedWorkService.getPlannedWorkByCollectionPlatform("B"))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findByCollectionPlatform("B");
    }

    @Test
    void testGetPlannedWorkBySize() {
        when(plannedWorkRepository.findBySize("L1"))
                .thenReturn(Flux.just(plannedWork));

        StepVerifier.create(plannedWorkService.getPlannedWorkBySize("L1"))
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findBySize("L1");
    }

    @Test
    void testGetUpcomingPlannedWork() {
        when(plannedWorkRepository.findUpcomingWork())
                .thenReturn(Flux.just(plannedWork));

        StepVerifier.create(plannedWorkService.getUpcomingPlannedWork())
                .expectNext(plannedWork)
                .verifyComplete();

        verify(plannedWorkRepository).findUpcomingWork();
    }

    @Test
    void testDeletePlannedWork() {
        when(plannedWorkRepository.findById(1L))
                .thenReturn(Mono.just(plannedWork));
        when(plannedWorkRepository.softDeleteById(1L))
                .thenReturn(Mono.just(1));

        StepVerifier.create(plannedWorkService.deletePlannedWork(1L))
                .verifyComplete();

        verify(plannedWorkRepository).findById(1L);
        verify(plannedWorkRepository).softDeleteById(1L);
    }

    @Test
    void testDeletePlannedWorkNotFound() {
        when(plannedWorkRepository.findById(999L))
                .thenReturn(Mono.empty());

        StepVerifier.create(plannedWorkService.deletePlannedWork(999L))
                .expectError(IllegalArgumentException.class)
                .verify();

        verify(plannedWorkRepository).findById(999L);
    }
}
