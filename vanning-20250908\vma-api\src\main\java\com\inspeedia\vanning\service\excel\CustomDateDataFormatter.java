package com.inspeedia.vanning.service.excel;

import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

class CustomDateDataFormatter extends DataFormatter {

    private final SimpleDateFormat isoDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
    private final SimpleDateFormat timeFormatWithSeconds = new SimpleDateFormat("HH:mm:ss");

    CustomDateDataFormatter(Locale locale) {
        super(locale);
        isoDateFormat.setLenient(false);
    }

    @Override
    public String formatRawCellContents(double value, int formatIndex, String formatString, boolean use1904Windowing) {
        try {
            if (DateUtil.isADateFormat(formatIndex, formatString)) {
                String f = formatString == null ? "" : formatString.toLowerCase(Locale.ROOT);
                boolean hasYear = f.contains("y");
                boolean hasDay = f.contains("d");
                boolean hasHour = f.contains("h");
                boolean hasSecond = f.contains("s");

                Date date = DateUtil.getJavaDate(value, use1904Windowing);

                // Time-only
                if (hasHour && !hasYear && !hasDay) {
                    return (hasSecond ? timeFormatWithSeconds : timeFormat).format(date);
                }

                // Date or DateTime → return date part only per import requirement
                return isoDateFormat.format(date);
            }
        } catch (Exception ignored) {
        }
        return super.formatRawCellContents(value, formatIndex, formatString, use1904Windowing);
    }
}


