import type { Task } from '../models/Task'

export const mockTasks: Task[] = [
	{
		id: 1,
		name: 'Task Alpha',
		no: 'T001',
		shppingDate: '2024-01-15',
		vangp: 'VGP001',
		deliveryTime: '09:00',
		plannedStart: '08:00',
		plannedEnd: '17:00',
		plannedDuration: '9h',
		actualStart: '08:15',
		actualEnd: '17:30',
		actualDuration: '9h 15m',
		progress: 85,
	},
	{
		id: 2,
		name: 'Task Beta',
		no: 'T002',
		shppingDate: '2024-01-16',
		vangp: 'VGP002',
		deliveryTime: '14:00',
		plannedStart: '13:00',
		plannedEnd: '18:00',
		plannedDuration: '5h',
		actualStart: '13:00',
		actualEnd: '18:00',
		actualDuration: '5h',
		progress: 100,
	},
	{
		id: 3,
		name: 'Task Gamma',
		no: 'T003',
		shppingDate: '2024-01-17',
		vangp: 'VGP003',
		deliveryTime: '16:00',
		plannedStart: '15:00',
		plannedEnd: '19:00',
		plannedDuration: '4h',
		actualStart: '',
		actualEnd: '',
		actualDuration: '',
		progress: 0,
	},
]

export const mockTask: Task = mockTasks[0]

export const mockEmptyTasks: Task[] = []
