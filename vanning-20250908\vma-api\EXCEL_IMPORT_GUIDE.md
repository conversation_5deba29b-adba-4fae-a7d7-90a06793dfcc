# Excel Import Guide for Planned Work (Japanese Format)

This guide explains how to use the Excel import functionality to bulk import planned work data into the VMA system using the Japanese Excel format.

## Overview

The Excel import feature allows you to:

- Import multiple planned work records from a multi-sheet Excel file
- Each sheet represents one user's planned work data
- Update existing records if they already exist in the database (same Van GP + same date)
- Insert new records if they don't exist
- Get detailed feedback on import results including errors and warnings
- Support for Japanese headers and alphanumeric Van GP values

## API Endpoints

### 1. Download Excel Template

**GET** `/api/v1/planned-work/import/template`

Downloads a pre-formatted Excel template with:

- Proper column headers
- Sample data row
- Field descriptions and format requirements
- Styling for better readability

### 2. Import Planned Work from Excel

**POST** `/api/v1/planned-work/import`

Imports planned work data from an uploaded Excel file.

**Request:**

- Content-Type: `multipart/form-data`
- Parameter: `file` (Excel file - .xlsx or .xls)

**Response:**

```json
{
  "totalRecords": 10,
  "successfulInserts": 7,
  "successfulUpdates": 2,
  "failedRecords": 1,
  "errors": ["Row 5: Invalid date format: 2024-13-01"],
  "warnings": [],
  "message": "Import completed: 7 inserted, 2 updated, 1 failed"
}
```

## Excel File Format (Japanese)

### Multi-Sheet Structure:

- Each sheet represents one user's planned work data
- Sheet names can be customized (e.g., "User1", "南中 ①", etc.)
- All sheets in the workbook will be processed

### Required Columns (Japanese Headers):

| Column | Header                               | Format           | Description                   | Example     |
| ------ | ------------------------------------ | ---------------- | ----------------------------- | ----------- |
| A      | (empty)                              | Text             | Row identifier                | B1, B2, B3  |
| B      | date                                 | YYYY/M/D         | Work date                     | 2025/9/5    |
| C      | 出荷日                               | Text             | Shipping info                 | 当日, 泊り  |
| D      | VANGP                                | 1-3 alphanumeric | Van group                     | PR, OU, V3  |
| E      | 搬入時間                             | H:MM or HH:MM    | Load time                     | 8:31, 13:21 |
| F      | 開始時間                             | H:MM or HH:MM    | Start time                    | 8:36, 13:26 |
| G      | 終了時間                             | H:MM or HH:MM    | End time                      | 9:26, 14:16 |
| H      | 作業時間                             | H:MM             | Work duration                 | 0:50        |
| I-M    | VS, 開始時間, 終了時間, 作業時間, VS | -                | Actual work columns (ignored) | -           |

### Field Validation Rules:

1. **Date (B)**: Must be a valid date in YYYY/M/D format (e.g., 2025/9/5)
2. **Van GP (D)**: 1-3 alphanumeric characters (A-Z, 0-9) - e.g., PR, OU, V3, II
3. **Load Time (E)**: Valid time in H:MM or HH:MM format
4. **Start Time (F)**: Valid time in H:MM or HH:MM format
5. **End Time (G)**: Valid time in H:MM or HH:MM format
6. **Duration (H)**: Valid time in H:MM format

### Default Values:

- **Delivery Platform**: "A" (automatically set)
- **Collection Platform**: "B" (automatically set)
- **Size**: "L1" (automatically set)

### Sample Data:

```
Row | date     | 出荷日 | VANGP | 搬入時間 | 開始時間 | 終了時間 | 作業時間
B1  | 2025/9/5 | 当日   | PR    | 8:31     | 8:36     | 9:26     | 0:50
B2  | 2025/9/5 | 当日   | OU    | 9:31     | 9:36     | 10:26    | 0:50
B3  | 2025/9/5 |        | OU    | 10:51    | 10:56    | 11:46    | 0:50
B10 | 2025/9/5 | 泊り   | V3    | 9:16     |          |          | 0:50
```

## Import Logic

### Duplicate Detection

Records are considered duplicates if they have the same combination of:

- Date
- Van GP
- Delivery Platform
- Collection Platform

### Insert vs Update

- **Insert**: If no existing record matches the combination above
- **Update**: If an existing record matches, it will be updated with new values for:
  - Load Time
  - Size
  - Start Time
  - End Time
  - Duration

## Error Handling

### File Validation

- Only .xlsx and .xls files are accepted
- File must contain at least one sheet
- First row must contain correct headers

### Data Validation

- Each field is validated according to its format requirements
- Invalid records are skipped and reported in the error list
- Row numbers are provided for easy identification

### Common Errors

1. **Invalid file type**: "Invalid file type. Please upload an Excel file (.xlsx or .xls)"
2. **Missing headers**: "Excel file must contain a header row"
3. **Invalid header**: "Invalid header at column 2. Expected: Van GP"
4. **Invalid date**: "Row 3: Invalid date format: 2024-13-01"
5. **Invalid Van GP**: "Row 4: Van GP must be 2 uppercase alphabets"
6. **Invalid time**: "Row 5: Invalid Load Time format: 25:00"

## Usage Instructions

### Step 1: Download Template

1. Access the Swagger UI at `http://localhost:8080/swagger-ui.html`
2. Navigate to "Planned Work Management" section
3. Use the "Download Excel template" endpoint to get the template file
4. Save the file as `planned_work_template.xlsx`

### Step 2: Prepare Your Data

1. Open the downloaded template in Excel
2. Keep the header row (row 1) unchanged
3. Replace the sample data (row 3) with your actual data
4. Add additional rows as needed
5. Ensure all data follows the format requirements

### Step 3: Import Data

1. In Swagger UI, use the "Import planned work from Excel file" endpoint
2. Click "Choose File" and select your prepared Excel file
3. Click "Execute" to start the import
4. Review the response for import results

### Step 4: Review Results

- Check `successfulInserts` and `successfulUpdates` for successful operations
- Review `errors` array for any failed records
- Fix any errors in your Excel file and re-import if needed

## Best Practices

1. **Always download the latest template** to ensure correct format
2. **Validate your data** before importing to minimize errors
3. **Start with small batches** to test the import process
4. **Keep backups** of your Excel files for reference
5. **Review error messages carefully** and fix issues systematically

## Troubleshooting

### Common Issues:

**Issue**: "Invalid file type" error
**Solution**: Ensure file has .xlsx or .xls extension

**Issue**: "Invalid header" error
**Solution**: Use the downloaded template and don't modify headers

**Issue**: Multiple validation errors
**Solution**: Check date formats, ensure Van GP is 2 letters, platforms are 1 letter

**Issue**: Time format errors
**Solution**: Use 24-hour format (HH:MM), ensure times are valid (00:00-23:59)

### Support

For additional support or questions about the Excel import functionality, please contact the development team or refer to the API documentation in Swagger UI.
