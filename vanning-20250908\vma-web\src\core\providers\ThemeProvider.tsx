import { Css<PERSON><PERSON><PERSON>, Theme<PERSON><PERSON><PERSON> as MuiThemeProvider, createTheme } from '@mui/material'
import { type PropsWithChildren, useMemo } from 'react'
import { useAppStore } from '../store'

export default function ThemeProvider({ children }: PropsWithChildren) {
	const mode = useAppStore((s) => s.themeMode)
	const fontScale = useAppStore((s) => s.fontScale)
	const theme = useMemo(
		() =>
			createTheme({
				spacing: 8,
				typography: {
					htmlFontSize: 16 / fontScale,
				},
				palette: {
					mode,
					primary: {
						main: mode === 'light' ? '#1976D2' : '#90CAF9',
						contrastText: '#FFFFFF',
						dark: mode === 'light' ? '#1565C0' : '#64B5F6',
						light: mode === 'light' ? '#42A5F5' : '#BBDEFB',
					},
					secondary: {
						main: mode === 'light' ? '#757575' : '#BDBDBD',
						contrastText: '#FFFFFF',
						dark: mode === 'light' ? '#616161' : '#9E9E9E',
						light: mode === 'light' ? '#9E9E9E' : '#E0E0E0',
					},
					background: {
						default: mode === 'light' ? '#FAFAFA' : '#121212',
						paper: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
					},
					text: {
						primary: mode === 'light' ? '#212121' : '#FFFFFF',
						secondary: mode === 'light' ? '#757575' : '#BDBDBD',
					},
					divider: mode === 'light' ? '#E0E0E0' : '#424242',
					action: {
						hover:
							mode === 'light' ? 'rgba(0, 0, 0, 0.04)' : 'rgba(255, 255, 255, 0.08)',
						selected:
							mode === 'light'
								? 'rgba(25, 118, 210, 0.08)'
								: 'rgba(144, 202, 249, 0.16)',
						disabled:
							mode === 'light' ? 'rgba(0, 0, 0, 0.26)' : 'rgba(255, 255, 255, 0.3)',
						disabledBackground:
							mode === 'light' ? 'rgba(0, 0, 0, 0.12)' : 'rgba(255, 255, 255, 0.12)',
					},
					success: {
						main: mode === 'light' ? '#4CAF50' : '#81C784',
						contrastText: '#FFFFFF',
					},
					warning: {
						main: mode === 'light' ? '#FF9800' : '#FFB74D',
						contrastText: '#FFFFFF',
					},
					error: {
						main: mode === 'light' ? '#F44336' : '#E57373',
						contrastText: '#FFFFFF',
					},
					info: {
						main: mode === 'light' ? '#2196F3' : '#64B5F6',
						contrastText: '#FFFFFF',
					},
				},
				shape: { borderRadius: 10 },
				components: {
					MuiButton: {
						styleOverrides: {
							root: {
								textTransform: 'none',
								borderRadius: 8,
								fontWeight: 500,
							},
							contained: {
								boxShadow: 'none',
								'&:hover': {
									boxShadow:
										mode === 'light'
											? '0 2px 4px rgba(0,0,0,0.1)'
											: '0 2px 4px rgba(0,0,0,0.3)',
								},
							},
							outlined: {
								borderWidth: 1.5,
								'&:hover': {
									borderWidth: 1.5,
									backgroundColor:
										mode === 'light'
											? 'rgba(25, 118, 210, 0.04)'
											: 'rgba(144, 202, 249, 0.08)',
								},
							},
							text: {
								'&:hover': {
									backgroundColor:
										mode === 'light'
											? 'rgba(25, 118, 210, 0.04)'
											: 'rgba(144, 202, 249, 0.08)',
								},
							},
						},
					},
					MuiPaper: {
						defaultProps: { elevation: 0 },
						styleOverrides: {
							root: {
								padding: 12,
								border:
									mode === 'light' ? '1px solid #E0E0E0' : '1px solid #424242',
								backgroundColor: mode === 'light' ? '#FFFFFF' : '#1E1E1E',
							},
						},
					},
					MuiCard: {
						styleOverrides: {
							root: {
								borderRadius: 12,
							},
						},
					},
					MuiAppBar: {
						styleOverrides: {
							root: {
								backgroundColor: mode === 'light' ? '#1976D2' : '#1E1E1E',
								color: '#FFFFFF',
								boxShadow:
									mode === 'light'
										? '0 2px 4px rgba(0,0,0,0.1)'
										: '0 2px 4px rgba(0,0,0,0.3)',
							},
						},
					},
					MuiIconButton: {
						styleOverrides: {
							root: {
								'&.MuiIconButton-colorInherit': {
									color: '#FFFFFF',
									'&:hover': {
										backgroundColor:
											mode === 'light'
												? 'rgba(255, 255, 255, 0.1)'
												: 'rgba(255, 255, 255, 0.05)',
									},
								},
							},
						},
					},
					MuiToolbar: {
						styleOverrides: {
							dense: {
								minHeight: 40,
							},
						},
					},
					MuiTableCell: {
						styleOverrides: {
							root: {
								paddingBlock: 12,
								paddingInline: 16,
								borderBottom: `1px solid ${mode === 'light' ? '#E0E0E0' : '#424242'}`,
							},
							head: {
								backgroundColor: mode === 'light' ? '#F5F5F5' : '#2C2C2C',
								fontWeight: 600,
								color: mode === 'light' ? '#212121' : '#FFFFFF',
							},
						},
					},
					MuiTableRow: {
						styleOverrides: {
							root: {
								'&:hover': {
									backgroundColor:
										mode === 'light'
											? 'rgba(25, 118, 210, 0.04)'
											: 'rgba(144, 202, 249, 0.08)',
								},
								'&.Mui-selected': {
									backgroundColor:
										mode === 'light'
											? 'rgba(25, 118, 210, 0.08)'
											: 'rgba(144, 202, 249, 0.16)',
									'&:hover': {
										backgroundColor:
											mode === 'light'
												? 'rgba(25, 118, 210, 0.12)'
												: 'rgba(144, 202, 249, 0.24)',
									},
								},
							},
						},
					},
					MuiTypography: {
						styleOverrides: {
							root: {
								color: 'inherit',
							},
						},
					},
				},
			}),
		[mode, fontScale],
	)
	return (
		<MuiThemeProvider theme={theme}>
			<CssBaseline />
			{children}
		</MuiThemeProvider>
	)
}
