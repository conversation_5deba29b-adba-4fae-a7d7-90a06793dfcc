import {
	Paper,
	Typography,
	useTheme,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Box,
	IconButton,
	Tooltip
} from '@mui/material'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import { Bar, Bar<PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip as RechartsT<PERSON>tip, <PERSON>Axis, <PERSON>Axis } from 'recharts'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'

interface ChartCardProps {
	selectedTask: Task | null
	onFullView?: (content: React.ReactNode, title?: string) => void
}

function areTasksEqual(a: Task | null, b: Task | null): boolean {
	if (a === b) return true
	if (!a || !b) return a === b
	return (
		a.id === b.id &&
		a.name === b.name &&
		a.progress === b.progress &&
		a.plannedStart === b.plannedStart &&
		a.plannedEnd === b.plannedEnd &&
		a.plannedDuration === b.plannedDuration &&
		a.actualStart === b.actualStart &&
		a.actualEnd === b.actualEnd &&
		a.actualDuration === b.actualDuration
	)
}

function ChartCardComponent({ selectedTask, onFullView }: ChartCardProps) {
	const theme = useTheme()
	const { t } = useTranslation()

	if (!selectedTask) {
		return (
			<Paper className="p-3 sm:p-4">
				<Typography variant="subtitle1" color="text.secondary">
					Select a task to view details
				</Typography>
			</Paper>
		)
	}

	const data = [{ name: selectedTask.name, value: selectedTask.progress }]

	const timeData = [
		{
			type: 'Planned',
			start: selectedTask.plannedStart,
			end: selectedTask.plannedEnd,
			duration: selectedTask.plannedDuration,
		},
		{
			type: 'Actual',
			start: selectedTask.actualStart,
			end: selectedTask.actualEnd,
			duration: selectedTask.actualDuration,
		},
	]

	return (
		<Paper className="p-3 sm:p-4 space-y-3 sm:space-y-4 grid grid-rows-auto h-full">
			{/* Header with title and fullscreen */}
			{selectedTask ? (
				<Box className="flex items-center justify-between">
					<Typography variant="subtitle1" fontWeight={600}>
						{selectedTask.name} - {t('chart.progress')}: {selectedTask.progress}%
					</Typography>
					{onFullView ? (
						<Tooltip title={t('common.fullscreen') ?? 'Fullscreen'}>
							<IconButton
								size="small"
								aria-label="fullscreen chart"
								onClick={() =>
									onFullView(
										<Box
											sx={{
												width: '90vw',
												height: '80vh',
												display: 'flex',
												alignItems: 'center',
												justifyContent: 'center'
											}}
										>
											<ResponsiveContainer width="100%" height="100%">
												<BarChart
													data={data}
													margin={{
														top: 40,
														right: 40,
														left: 40,
														bottom: 40,
													}}
												>
													<CartesianGrid
														stroke={theme.palette.divider}
														strokeDasharray="3 3"
													/>
													<XAxis
														dataKey="name"
														stroke={theme.palette.text.secondary}
														fontSize={16}
														tick={{
															fill: theme.palette.text.secondary,
														}}
													/>
													<YAxis
														domain={[0, 100]}
														ticks={[0, 25, 50, 75, 100]}
														stroke={theme.palette.text.secondary}
														fontSize={16}
														tick={{
															fill: theme.palette.text.secondary,
														}}
													/>
													<RechartsTooltip
														contentStyle={{
															backgroundColor:
																theme.palette.background.paper,
															border: `1px solid ${theme.palette.divider}`,
															borderRadius: 8,
															color: theme.palette.text.primary,
															fontSize: 14,
														}}
													/>
													<Bar
														dataKey="value"
														fill={theme.palette.primary.main}
														radius={[4, 4, 0, 0]}
													/>
												</BarChart>
											</ResponsiveContainer>
										</Box>,
										`${selectedTask.name}`,
									)
								}
							>
								<FullscreenIcon fontSize="small" />
							</IconButton>
						</Tooltip>
					) : null}
				</Box>
			) : null}
			{/* Time Data Table */}
			<Box>
				<TableContainer>
					<Table size="small">
						<TableHead>
							<TableRow>
								<TableCell>{t('table.type')}</TableCell>
								<TableCell align="center">{t('table.start')}</TableCell>
								<TableCell align="center">{t('table.end')}</TableCell>
								<TableCell align="center">{t('table.duration')}</TableCell>
							</TableRow>
						</TableHead>
						<TableBody>
							{timeData.map((row) => (
								<TableRow key={row.type}>
									<TableCell component="th" scope="row" sx={{ fontWeight: 500 }}>
										{row.type}
									</TableCell>
									<TableCell align="center">{row.start}</TableCell>
									<TableCell align="center">{row.end}</TableCell>
									<TableCell align="center">{row.duration}</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</TableContainer>
			</Box>

			{/* Chart Section */}
			<Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
				<Box
					sx={{
						width: '100%',
						maxWidth: { xs: 280, sm: 320 },
						height: { xs: 180, sm: 200 },
					}}
				>
					<ResponsiveContainer width="100%" height="100%">
						<BarChart data={data} margin={{ top: 16, right: 16, left: 16, bottom: 16 }}>
							<CartesianGrid stroke={theme.palette.divider} strokeDasharray="3 3" />
							<XAxis
								dataKey="name"
								stroke={theme.palette.text.secondary}
								fontSize={12}
								tick={{ fill: theme.palette.text.secondary }}
							/>
							<YAxis
								domain={[0, 100]}
								ticks={[0, 25, 50, 75, 100]}
								stroke={theme.palette.text.secondary}
								fontSize={12}
								tick={{ fill: theme.palette.text.secondary }}
							/>
							<RechartsTooltip
								contentStyle={{
									backgroundColor: theme.palette.background.paper,
									border: `1px solid ${theme.palette.divider}`,
									borderRadius: 8,
									color: theme.palette.text.primary,
								}}
							/>
							<Bar
								dataKey="value"
								fill={theme.palette.primary.main}
								radius={[4, 4, 0, 0]}
							/>
						</BarChart>
					</ResponsiveContainer>
				</Box>
			</Box>
		</Paper>
	)
}

const ChartCard = memo(ChartCardComponent, (prev, next) =>
	areTasksEqual(prev.selectedTask, next.selectedTask),
)

export default ChartCard
