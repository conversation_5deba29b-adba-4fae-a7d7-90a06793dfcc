import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.FileInputStream;
import java.io.IOException;

public class ExcelDebugger {
    public static void main(String[] args) {
        String filePath = "test-plan.xlsx";
        
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            System.out.println("=== Excel File Analysis ===");
            System.out.println("Number of sheets: " + workbook.getNumberOfSheets());
            
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                System.out.println("\n--- Sheet " + sheetIndex + ": " + sheet.getSheetName() + " ---");
                
                // Check first few rows
                for (int rowIndex = 0; rowIndex < Math.min(5, sheet.getLastRowNum() + 1); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row != null) {
                        System.out.print("Row " + rowIndex + ": ");
                        for (int colIndex = 0; colIndex < Math.min(10, row.getLastCellNum()); colIndex++) {
                            Cell cell = row.getCell(colIndex);
                            String value = getCellValueAsString(cell);
                            System.out.print("[" + colIndex + "]=" + value + " | ");
                        }
                        System.out.println();
                    }
                }
            }
            
        } catch (IOException e) {
            System.err.println("Error reading Excel file: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "NULL";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return "\"" + cell.getStringCellValue() + "\"";
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return "DATE(" + cell.getNumericCellValue() + ")";
                } else {
                    return "NUM(" + cell.getNumericCellValue() + ")";
                }
            case BOOLEAN:
                return "BOOL(" + cell.getBooleanCellValue() + ")";
            case FORMULA:
                return "FORMULA(" + cell.getCellFormula() + ")";
            case BLANK:
                return "BLANK";
            default:
                return "UNKNOWN";
        }
    }
}
