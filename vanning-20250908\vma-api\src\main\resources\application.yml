server:
  port: 8080
  shutdown: graceful
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: on_param
    include-exception: false

spring:
  application:
    name: vma-api
  config:
    import: classpath:excel-import.properties

  # R2DBC Configuration for MSSQL
  r2dbc:
    url: r2dbc:mssql://localhost:1433/vma_db
    username: ${DB_USERNAME:mssadmin}
    password: ${DB_PASSWORD:pass}
    pool:
      initial-size: 5
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1

  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
      accept-single-value-as-array: true
    time-zone: UTC

  # Security Configuration
  security:
    user:
      name: admin
      password: ${ADMIN_PASSWORD:admin123}
      roles: ADMIN

  # Lifecycle Configuration
  lifecycle:
    timeout-per-shutdown-phase: 30s

# Logging Configuration
logging:
  level:
    root: INFO
    com.inspeedia.vanning: DEBUG
    org.springframework.r2dbc: DEBUG
    io.r2dbc.mssql: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/vma-api.log
    max-size: 10MB
    max-history: 30
    total-size-cap: 1GB

# Management and Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: when_authorized
      show-components: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms

# Application Specific Configuration
app:
  cors:
    allowed-origins:
      - "http://localhost:3000"
      - "http://localhost:4200"
      - "http://localhost:5173"
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers:
      - "*"
    allow-credentials: true
    max-age: 3600

  api:
    version: v1
    base-path: /api/${app.api.version}

  pagination:
    default-page-size: 20
    max-page-size: 100

# OpenAPI Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: true
