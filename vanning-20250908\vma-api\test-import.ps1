# Test Excel Import API with simple file name
$baseUrl = "http://localhost:8080"
$excelFile = "test-plan.xlsx"

Write-Host "Testing Excel Import API..." -ForegroundColor Green
Write-Host "Excel file: $excelFile" -ForegroundColor Yellow

try {
    # Test with Invoke-WebRequest for better control
    $form = @{
        file = Get-Item -Path $excelFile
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/api/planned-work/import" -Method Post -Form $form
    
    Write-Host "Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
    
} catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" -ForegroundColor Yellow
        Write-Host $responseBody -ForegroundColor Yellow
    }
}
