<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ExcelImportServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ExcelImportServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.inspeedia.vanning.service.html">com.inspeedia.vanning.service</a> &gt; ExcelImportServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">2</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">4.762s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testImportPlannedWorkFromExcel_InvalidFile()</td>
<td class="success">4.610s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testImportPlannedWorkFromExcel_Success()</td>
<td class="success">0.152s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>10:46:43.148 [Test worker] INFO com.inspeedia.vanning.service.ExcelImportService -- Starting Excel import for file: 計画.xlsx
10:46:43.535 [Test worker] ERROR com.inspeedia.vanning.service.ExcelImportService -- Streaming import failed: Invalid file
10:46:43.536 [Test worker] ERROR com.inspeedia.vanning.service.ExcelImportService -- Excel import failed: Invalid file
10:46:43.673 [Test worker] INFO com.inspeedia.vanning.service.ExcelImportService -- Starting Excel import for file: 計画.xlsx
10:46:43.699 [Test worker] INFO com.inspeedia.vanning.service.ExcelImportService -- Excel import completed: ImportResultDto{totalRecords=0, successfulInserts=0, successfulUpdates=0, failedRecords=0, errors=[], warnings=[], message='Import completed: 0 saved, 0 failed'}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 8, 2025, 10:46:43 AM</p>
</div>
</div>
</body>
</html>
