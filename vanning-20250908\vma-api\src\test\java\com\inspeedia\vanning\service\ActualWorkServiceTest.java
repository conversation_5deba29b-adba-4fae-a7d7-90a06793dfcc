package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.repository.ActualWorkRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

/**
 * Unit tests for ActualWorkService
 */
@ExtendWith(MockitoExtension.class)
class ActualWorkServiceTest {

    @Mock
    private ActualWorkRepository actualWorkRepository;

    @InjectMocks
    private ActualWorkService actualWorkService;

    private ActualWork actualWork;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        actualWork = new ActualWork();
        actualWork.setId(1L);
        actualWork.setOperatorName("田中太郎");
        actualWork.setWorkDate(LocalDate.of(2024, 1, 15));
        actualWork.setStartTime(LocalTime.of(8, 0));
        actualWork.setEndTime(LocalTime.of(17, 0));
        actualWork.setDuration(LocalTime.of(9, 0));
        actualWork.setProgress(8);
        actualWork.setProgressRate(80.0f);
        actualWork.setVanGp("AB");
        actualWork.setCompleted(false);
    }

    @Test
    void testCreateActualWork() {
        when(actualWorkRepository.save(any(ActualWork.class)))
                .thenReturn(Mono.just(actualWork));

        ActualWork newWork = new ActualWork("田中太郎", LocalDate.of(2024, 1, 15), LocalTime.of(8, 0), LocalTime.of(17, 0), LocalTime.of(9, 0), 8, 80.0f, "AB", false);

        StepVerifier.create(actualWorkService.createActualWork(newWork))
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).save(any(ActualWork.class));
    }

    @Test
    void testUpdateActualWork() {
        when(actualWorkRepository.findById(1L))
                .thenReturn(Mono.just(actualWork));
        when(actualWorkRepository.save(any(ActualWork.class)))
                .thenReturn(Mono.just(actualWork));

        ActualWork updateData = new ActualWork("佐藤花子", LocalDate.of(2024, 1, 16), LocalTime.of(9, 0), LocalTime.of(18, 0), LocalTime.of(9, 0), 10, 100.0f, "CD", true);

        StepVerifier.create(actualWorkService.updateActualWork(1L, updateData))
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).findById(1L);
        verify(actualWorkRepository).save(any(ActualWork.class));
    }

    @Test
    void testUpdateActualWorkNotFound() {
        when(actualWorkRepository.findById(999L))
                .thenReturn(Mono.empty());

        ActualWork updateData = new ActualWork("佐藤花子", LocalDate.of(2024, 1, 16), LocalTime.of(9, 0), LocalTime.of(18, 0), LocalTime.of(9, 0), 10, 100.0f, "CD", true);

        StepVerifier.create(actualWorkService.updateActualWork(999L, updateData))
                .expectError(IllegalArgumentException.class)
                .verify();

        verify(actualWorkRepository).findById(999L);
    }

    @Test
    void testGetActualWorkById() {
        when(actualWorkRepository.findById(1L))
                .thenReturn(Mono.just(actualWork));

        StepVerifier.create(actualWorkService.getActualWorkById(1L))
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).findById(1L);
    }

    @Test
    void testGetActualWorkByIdNotFound() {
        when(actualWorkRepository.findById(999L))
                .thenReturn(Mono.empty());

        StepVerifier.create(actualWorkService.getActualWorkById(999L))
                .expectError(IllegalArgumentException.class)
                .verify();

        verify(actualWorkRepository).findById(999L);
    }

    @Test
    void testGetAllActiveActualWork() {
        ActualWork work1 = new ActualWork("田中太郎", LocalDate.of(2024, 1, 15), LocalTime.of(8, 0), LocalTime.of(17, 0), LocalTime.of(9, 0), 8, 80.0f, "AB", false);
        ActualWork work2 = new ActualWork("佐藤花子", LocalDate.of(2024, 1, 15), LocalTime.of(9, 0), LocalTime.of(18, 0), LocalTime.of(9, 0), 10, 100.0f, "CD", true);

        when(actualWorkRepository.findAllActive(0L, 20))
                .thenReturn(Flux.just(work1, work2));

        StepVerifier.create(actualWorkService.getAllActiveActualWork(0, 20))
                .expectNext(work1)
                .expectNext(work2)
                .verifyComplete();

        verify(actualWorkRepository).findAllActive(0L, 20);
    }

    @Test
    void testGetActualWorkByUserName() {
        when(actualWorkRepository.findByOperatorName("田中太郎"))
                .thenReturn(Flux.just(actualWork));

        StepVerifier.create(actualWorkService.getActualWorkByUserName("田中太郎"))
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).findByOperatorName("田中太郎");
    }

    @Test
    void testGetActualWorkByProgress() {
        when(actualWorkRepository.findByProgress(8))
                .thenReturn(Flux.just(actualWork));

        StepVerifier.create(actualWorkService.getActualWorkByProgress(8))
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).findByProgress(8);
    }

    @Test
    void testGetHighProgressWork() {
        when(actualWorkRepository.findHighProgressWork())
                .thenReturn(Flux.just(actualWork));

        StepVerifier.create(actualWorkService.getHighProgressWork())
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).findHighProgressWork();
    }

    @Test
    void testGetLowProgressWork() {
        ActualWork lowProgressWork = new ActualWork("山田一郎", LocalDate.of(2024, 1, 17), LocalTime.of(8, 0), LocalTime.of(17, 0), LocalTime.of(9, 0), 3, 30.0f, "EF", false);

        when(actualWorkRepository.findLowProgressWork())
                .thenReturn(Flux.just(lowProgressWork));

        StepVerifier.create(actualWorkService.getLowProgressWork())
                .expectNext(lowProgressWork)
                .verifyComplete();

        verify(actualWorkRepository).findLowProgressWork();
    }

    @Test
    void testDeleteActualWork() {
        when(actualWorkRepository.findById(1L))
                .thenReturn(Mono.just(actualWork));
        when(actualWorkRepository.softDeleteById(1L))
                .thenReturn(Mono.just(1));

        StepVerifier.create(actualWorkService.deleteActualWork(1L))
                .verifyComplete();

        verify(actualWorkRepository).findById(1L);
        verify(actualWorkRepository).softDeleteById(1L);
    }

    @Test
    void testDeleteActualWorkNotFound() {
        when(actualWorkRepository.findById(999L))
                .thenReturn(Mono.empty());

        StepVerifier.create(actualWorkService.deleteActualWork(999L))
                .expectError(IllegalArgumentException.class)
                .verify();

        verify(actualWorkRepository).findById(999L);
    }

    @Test
    void testCountActualWorkByUserName() {
        when(actualWorkRepository.countByUserName("田中太郎"))
                .thenReturn(Mono.just(5L));

        StepVerifier.create(actualWorkService.countActualWorkByUserName("田中太郎"))
                .expectNext(5L)
                .verifyComplete();

        verify(actualWorkRepository).countByUserName("田中太郎");
    }

    @Test
    void testGetActualWorkByStartTimeRange() {
        LocalTime start = LocalTime.of(8, 0);
        LocalTime end = LocalTime.of(10, 0);

        when(actualWorkRepository.findByStartTimeBetween(start, end))
                .thenReturn(Flux.just(actualWork));

        StepVerifier.create(actualWorkService.getActualWorkByStartTimeRange(start, end))
                .expectNext(actualWork)
                .verifyComplete();

        verify(actualWorkRepository).findByStartTimeBetween(start, end);
    }
}
