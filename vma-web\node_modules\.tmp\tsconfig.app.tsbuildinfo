{"root": ["../../src/app.tsx", "../../src/jest.d.ts", "../../src/main.tsx", "../../src/setuptests.ts", "../../src/test-utils.tsx", "../../src/vite-env.d.ts", "../../src/__mocks__/mockdata.ts", "../../src/app/dashboard/index.ts", "../../src/app/dashboard/components/chartcard.tsx", "../../src/app/dashboard/components/datatable.tsx", "../../src/app/dashboard/components/elementcard.tsx", "../../src/app/dashboard/components/__tests__/chartcard.test.tsx", "../../src/app/dashboard/components/__tests__/datatable.test.tsx", "../../src/app/dashboard/components/__tests__/elementcard.test.tsx", "../../src/app/dashboard/services/tasks.ts", "../../src/app/dashboard/services/__tests__/tasks.test.ts", "../../src/app/layout/index.tsx", "../../src/app/layout/footer/index.tsx", "../../src/app/layout/header/index.tsx", "../../src/app/layout/sidebar/index.tsx", "../../src/app/routes/protectedroute.tsx", "../../src/app/routes/publicroute.tsx", "../../src/app/routes/index.tsx", "../../src/app/settings/index.ts", "../../src/app/settings/services/plannedworkimport.ts", "../../src/app/settings/services/__tests__/plannedworkimport.test.ts", "../../src/common/components/fullscreenmodal.tsx", "../../src/common/components/loader.tsx", "../../src/common/components/__tests__/loader.test.tsx", "../../src/common/hooks/useorientation.ts", "../../src/common/hooks/__tests__/useorientation.test.ts", "../../src/core/api/endpoints.ts", "../../src/core/api/httpclient.ts", "../../src/core/api/__tests__/httpclient.test.ts", "../../src/core/config/env.ts", "../../src/core/error/errorboundary.tsx", "../../src/core/i18n/index.ts", "../../src/core/providers/authprovider.tsx", "../../src/core/providers/themeprovider.tsx", "../../src/core/store/index.ts", "../../src/core/utils/settings.ts", "../../src/core/utils/__tests__/settings.test.ts", "../../src/layout/rootlayout.tsx", "../../src/mocks/browser.ts", "../../src/mocks/handlers.ts", "../../src/models/plannedworkimport.ts", "../../src/models/task.ts", "../../src/pages/dashboardpage.tsx", "../../src/pages/settingspage.tsx", "../../src/pages/__tests__/dashboardpage.test.tsx"], "version": "5.8.3"}