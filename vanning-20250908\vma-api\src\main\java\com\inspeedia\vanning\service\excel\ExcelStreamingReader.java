package com.inspeedia.vanning.service.excel;

import com.inspeedia.vanning.dto.RowDto;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler;
import org.apache.poi.xssf.model.StylesTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Component
public class ExcelStreamingReader {

    private final Logger log = LoggerFactory.getLogger(ExcelStreamingReader.class);

    public Flux<RowDto> read(InputStream inputStream) {
        return Flux.defer(() -> {
            try {
                OPCPackage pkg = OPCPackage.open(inputStream);
                XSSFReader reader = new XSSFReader(pkg);
                StylesTable styles = reader.getStylesTable();
                ReadOnlySharedStringsTable sst = new ReadOnlySharedStringsTable(pkg);
                XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) reader.getSheetsData();

                List<Flux<RowDto>> sheetFluxes = new ArrayList<>();
                while (iter.hasNext()) {
                    InputStream sheetStream = iter.next();
                    String sheetName = iter.getSheetName();
                    Flux<RowDto> sheetFlux = Flux.<RowDto>create(sink -> {
                                try (InputStream ss = sheetStream) {
                                    javax.xml.parsers.SAXParserFactory spf = javax.xml.parsers.SAXParserFactory.newInstance();
                                    spf.setNamespaceAware(true);
                                    try {
                                        spf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
                                        spf.setFeature("http://xml.org/sax/features/external-general-entities", false);
                                        spf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
                                        spf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
                                    } catch (Exception ignored) {}
                                    org.xml.sax.XMLReader parser = spf.newSAXParser().getXMLReader();
                                    try {
                                        parser.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
                                        parser.setFeature("http://xml.org/sax/features/external-general-entities", false);
                                        parser.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
                                        parser.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
                                    } catch (org.xml.sax.SAXNotRecognizedException | org.xml.sax.SAXNotSupportedException ignored) {}
                                    XSSFSheetXMLHandler.SheetContentsHandler handler = new ReactiveSheetHandler(sheetName, sink);
                                    org.apache.poi.ss.usermodel.DataFormatter fmt = new CustomDateDataFormatter(Locale.getDefault());
                                    XSSFSheetXMLHandler xssfHandler = new XSSFSheetXMLHandler(styles, null, sst, handler, fmt, false);
                                    parser.setContentHandler(xssfHandler);
                                    parser.parse(new org.xml.sax.InputSource(ss));
                                    sink.complete();
                                } catch (Exception e) {
                                    sink.error(e);
                                }
                            })
                            .subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic());
                    sheetFluxes.add(sheetFlux);
                }

                return Flux.concat(sheetFluxes)
                        .doFinally(sig -> {
                            try { pkg.close(); } catch (Exception ignore) {}
                            try { inputStream.close(); } catch (Exception ignore) {}
                        });
            } catch (Exception e) {
                log.error("Failed to open Excel for streaming: {}", e.getMessage());
                return Flux.error(e);
            }
        }).subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic());
    }
}


