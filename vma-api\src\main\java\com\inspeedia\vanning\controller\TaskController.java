package com.inspeedia.vanning.controller;

import com.inspeedia.vanning.dto.TaskDto;
import com.inspeedia.vanning.service.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * REST Controller for Task operations
 * This controller provides reactive REST endpoints for managing tasks in
 * the VMA (Vanning Management Application) system. Tasks are a combined
 * view of planned and actual work data.
 */
@RestController
@RequestMapping("${app.api.base-path}/tasks")
@Tag(name = "Task Management", description = "APIs for managing tasks (combined planned and actual work)")
public class TaskController {

    private final Logger log = LoggerFactory.getLogger(TaskController.class);
    private final TaskService taskService;

    public TaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @Operation(summary = "Get all tasks", description = "Retrieves all tasks by combining planned and actual work data")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Tasks retrieved successfully")
    })
    @GetMapping
    public Flux<TaskDto> getAllTasks() {
        log.info("Fetching all tasks");
        return taskService.getAllTasks()
                .doOnComplete(() -> log.debug("Successfully fetched all tasks"))
                .doOnError(error -> log.error("Error fetching tasks: {}", error.getMessage()));
    }

    @Operation(summary = "Get today's tasks", description = "Retrieves tasks for today by combining planned and actual work data")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Today's tasks retrieved successfully")
    })
    @GetMapping("/today")
    public Flux<TaskDto> getTodaysTasks() {
        log.info("Fetching today's tasks");
        return taskService.getTodayTasksReactive()
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks"))
                .doOnError(error -> log.error("Error fetching today's tasks: {}", error.getMessage()));
    }
}
